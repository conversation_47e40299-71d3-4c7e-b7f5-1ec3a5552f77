[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mobius"
version = "0.1.0"
requires-python = ">=3.10"
dependencies = [
    "mobius-multi-agent",
    "mobius-multi-agent-server",
]

[[tool.uv.index]]
url = "https://mirrors.ustc.edu.cn/pypi/simple"
default = true

[tool.uv.sources]
mobius-common = { workspace = true }
mobius-multi-agent = { workspace = true }
mobius-multi-agent-server = { workspace = true }

[tool.uv.workspace]
members = ["packages/*"]

[tool.hatch.build.targets.wheel]
packages = ["src/mobius"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py310"
line-length = 88
include = ["src/**/*.py", "tests/**/*.py"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = []

[tool.mypy]
python_version = "3.10"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
files = ["packages", "tests"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --cov --cov-report=term-missing"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (integration tests)",
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
]
asyncio_mode = "auto"

[tool.vulture]
paths = ["src", "tests", "vulture_whitelist.py"]

[tool.coverage.run]
source = ["src", "packages"]  # Directories to measure coverage for
branch = true                  # Enable branch coverage measurement (if/else statements)
parallel = true                # Create separate .coverage.* files for parallel runs (enables combining later)
sigterm = true                 # Install SIGTERM handler to write coverage data on graceful shutdown (important for subprocesses)

[tool.coverage.report]
show_missing = true  # Show line numbers of statements that weren't executed

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "httpx>=0.28.1",
    "responses>=0.25.7",
    "requests>=2.32.4",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
    "vulture>=2.3",
    "tach>=0.29.0",
    "types-requests>=2.32.4.20250611",
    "pytest-cov>=6.2.1",
    "coverage>=7.10.2",
    "diff-cover>=9.0.0",
]
