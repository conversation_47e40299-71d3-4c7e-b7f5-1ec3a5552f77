"""
检查点和状态管理系统

实现 LangGraph checkpoint 机制和完整的状态持久化
"""

import json
import pickle
import sqlite3
import asyncio
from typing import Dict, Any, Optional, List, Union, Type
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum
import uuid

from pydantic import BaseModel, Field, ConfigDict
from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.checkpoint.sqlite import SqliteSaver

from .models import SupervisorGraphState, ExecutionPlan, SubTask, AgentResult


class CheckpointType(str, Enum):
    """检查点类型枚举"""
    
    PLAN_GENERATED = "plan_generated"  # 计划生成完成
    TASK_STARTED = "task_started"  # 任务开始执行
    TASK_COMPLETED = "task_completed"  # 任务完成
    TASK_FAILED = "task_failed"  # 任务失败
    USER_INTERACTION = "user_interaction"  # 用户交互
    PLAN_REVISED = "plan_revised"  # 计划修订
    EXECUTION_COMPLETED = "execution_completed"  # 执行完成
    ERROR_OCCURRED = "error_occurred"  # 发生错误
    MANUAL_CHECKPOINT = "manual_checkpoint"  # 手动检查点


class CheckpointRecord(BaseModel):
    """检查点记录"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="检查点ID")
    session_id: str = Field(..., description="会话ID")
    checkpoint_type: CheckpointType = Field(..., description="检查点类型")
    state_snapshot: Dict[str, Any] = Field(..., description="状态快照")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="创建时间")
    description: Optional[str] = Field(default=None, description="检查点描述")
    tags: List[str] = Field(default_factory=list, description="标签")
    parent_checkpoint_id: Optional[str] = Field(default=None, description="父检查点ID")
    
    def get_state(self) -> SupervisorGraphState:
        """获取状态对象"""
        return SupervisorGraphState.model_validate(self.state_snapshot)


class StateVersion(BaseModel):
    """状态版本"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    version: int = Field(..., description="版本号")
    checkpoint_id: str = Field(..., description="检查点ID")
    changes: List[str] = Field(default_factory=list, description="变更描述")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, storage_path: Optional[str] = None, use_sqlite: bool = True):
        self.storage_path = Path(storage_path) if storage_path else Path("checkpoints")
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化检查点保存器
        if use_sqlite:
            db_path = self.storage_path / "checkpoints.db"
            self.checkpoint_saver = SqliteSaver.from_conn_string(f"sqlite:///{db_path}")
        else:
            self.checkpoint_saver = InMemorySaver()
        
        # 检查点记录存储
        self.checkpoint_records: Dict[str, CheckpointRecord] = {}
        self.state_versions: Dict[str, List[StateVersion]] = {}
        
        # 加载现有检查点记录
        self._load_checkpoint_records()
    
    def _load_checkpoint_records(self) -> None:
        """加载检查点记录"""
        records_file = self.storage_path / "checkpoint_records.json"
        if records_file.exists():
            try:
                with open(records_file, 'r', encoding='utf-8') as f:
                    records_data = json.load(f)
                    for record_data in records_data:
                        record = CheckpointRecord(**record_data)
                        self.checkpoint_records[record.id] = record
            except Exception as e:
                print(f"加载检查点记录失败: {e}")
    
    def _save_checkpoint_records(self) -> None:
        """保存检查点记录"""
        records_file = self.storage_path / "checkpoint_records.json"
        try:
            records_data = [record.model_dump(mode='json') for record in self.checkpoint_records.values()]
            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"保存检查点记录失败: {e}")
    
    async def create_checkpoint(self, session_id: str, state: SupervisorGraphState,
                              checkpoint_type: CheckpointType, description: Optional[str] = None,
                              tags: Optional[List[str]] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """创建检查点"""
        # 创建检查点记录
        record = CheckpointRecord(
            session_id=session_id,
            checkpoint_type=checkpoint_type,
            state_snapshot=state.model_dump(),
            description=description,
            tags=tags or [],
            metadata=metadata or {}
        )
        
        # 保存到内存
        self.checkpoint_records[record.id] = record
        
        # 保存到持久化存储
        checkpoint = Checkpoint(
            v=1,
            ts=record.timestamp.isoformat(),
            id=record.id,
            channel_values={"state": state.model_dump()},
            channel_versions={"state": 1},
            versions_seen={"state": {"state": 1}}
        )
        
        checkpoint_metadata = CheckpointMetadata(
            source="supervisor",
            step=len(self.checkpoint_records),
            writes={"state": state.model_dump()},
            parents={}
        )
        
        # 使用 LangGraph 的检查点保存器
        await self.checkpoint_saver.aput(
            config={"configurable": {"thread_id": session_id}},
            checkpoint=checkpoint,
            metadata=checkpoint_metadata,
            new_versions={}
        )
        
        # 更新状态版本
        if session_id not in self.state_versions:
            self.state_versions[session_id] = []
        
        version = StateVersion(
            version=len(self.state_versions[session_id]) + 1,
            checkpoint_id=record.id,
            changes=[f"创建{checkpoint_type}检查点"]
        )
        self.state_versions[session_id].append(version)
        
        # 保存记录
        self._save_checkpoint_records()
        
        return record.id
    
    async def restore_checkpoint(self, checkpoint_id: str) -> Optional[SupervisorGraphState]:
        """恢复检查点"""
        if checkpoint_id not in self.checkpoint_records:
            return None
        
        record = self.checkpoint_records[checkpoint_id]
        try:
            return record.get_state()
        except Exception as e:
            print(f"恢复检查点失败: {e}")
            return None
    
    async def get_latest_checkpoint(self, session_id: str) -> Optional[CheckpointRecord]:
        """获取最新检查点"""
        session_checkpoints = [
            record for record in self.checkpoint_records.values()
            if record.session_id == session_id
        ]
        
        if not session_checkpoints:
            return None
        
        return max(session_checkpoints, key=lambda x: x.timestamp)
    
    async def list_checkpoints(self, session_id: str, checkpoint_type: Optional[CheckpointType] = None,
                             limit: Optional[int] = None) -> List[CheckpointRecord]:
        """列出检查点"""
        checkpoints = [
            record for record in self.checkpoint_records.values()
            if record.session_id == session_id
        ]
        
        if checkpoint_type:
            checkpoints = [cp for cp in checkpoints if cp.checkpoint_type == checkpoint_type]
        
        # 按时间倒序排列
        checkpoints.sort(key=lambda x: x.timestamp, reverse=True)
        
        if limit:
            checkpoints = checkpoints[:limit]
        
        return checkpoints
    
    async def delete_checkpoint(self, checkpoint_id: str) -> bool:
        """删除检查点"""
        if checkpoint_id not in self.checkpoint_records:
            return False
        
        record = self.checkpoint_records[checkpoint_id]
        
        # 从内存中删除
        del self.checkpoint_records[checkpoint_id]
        
        # 更新状态版本
        if record.session_id in self.state_versions:
            self.state_versions[record.session_id] = [
                v for v in self.state_versions[record.session_id]
                if v.checkpoint_id != checkpoint_id
            ]
        
        # 保存更改
        self._save_checkpoint_records()
        
        return True
    
    async def cleanup_old_checkpoints(self, session_id: str, keep_count: int = 10) -> int:
        """清理旧检查点"""
        checkpoints = await self.list_checkpoints(session_id)
        
        if len(checkpoints) <= keep_count:
            return 0
        
        # 保留最新的检查点
        to_delete = checkpoints[keep_count:]
        deleted_count = 0
        
        for checkpoint in to_delete:
            if await self.delete_checkpoint(checkpoint.id):
                deleted_count += 1
        
        return deleted_count
    
    async def create_state_diff(self, old_state: SupervisorGraphState, 
                              new_state: SupervisorGraphState) -> Dict[str, Any]:
        """创建状态差异"""
        old_dict = old_state.model_dump()
        new_dict = new_state.model_dump()
        
        diff = {}
        
        # 比较各个字段
        for key in new_dict:
            if key not in old_dict:
                diff[key] = {"action": "added", "value": new_dict[key]}
            elif old_dict[key] != new_dict[key]:
                diff[key] = {
                    "action": "modified",
                    "old_value": old_dict[key],
                    "new_value": new_dict[key]
                }
        
        # 检查删除的字段
        for key in old_dict:
            if key not in new_dict:
                diff[key] = {"action": "removed", "value": old_dict[key]}
        
        return diff
    
    async def get_checkpoint_statistics(self, session_id: str) -> Dict[str, Any]:
        """获取检查点统计"""
        checkpoints = await self.list_checkpoints(session_id)
        
        if not checkpoints:
            return {"total": 0, "by_type": {}, "timeline": []}
        
        # 按类型统计
        by_type = {}
        for checkpoint in checkpoints:
            checkpoint_type = checkpoint.checkpoint_type
            by_type[checkpoint_type] = by_type.get(checkpoint_type, 0) + 1
        
        # 时间线
        timeline = [
            {
                "checkpoint_id": cp.id,
                "type": cp.checkpoint_type,
                "timestamp": cp.timestamp,
                "description": cp.description
            }
            for cp in reversed(checkpoints[-20:])  # 最近20个
        ]
        
        return {
            "total": len(checkpoints),
            "by_type": by_type,
            "timeline": timeline,
            "latest_checkpoint": checkpoints[0].model_dump() if checkpoints else None,
            "storage_size": self._calculate_storage_size(session_id)
        }
    
    def _calculate_storage_size(self, session_id: str) -> int:
        """计算存储大小"""
        total_size = 0
        for record in self.checkpoint_records.values():
            if record.session_id == session_id:
                # 估算大小（字节）
                total_size += len(json.dumps(record.state_snapshot, default=str))
        return total_size
    
    async def export_session_checkpoints(self, session_id: str, export_path: Optional[str] = None) -> str:
        """导出会话检查点"""
        checkpoints = await self.list_checkpoints(session_id)
        
        export_data = {
            "session_id": session_id,
            "export_timestamp": datetime.now().isoformat(),
            "checkpoints": [cp.model_dump(mode='json') for cp in checkpoints],
            "state_versions": self.state_versions.get(session_id, [])
        }
        
        if not export_path:
            export_path = self.storage_path / f"export_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
        
        return str(export_path)
    
    async def import_session_checkpoints(self, import_path: str) -> str:
        """导入会话检查点"""
        with open(import_path, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        session_id = import_data["session_id"]
        
        # 导入检查点记录
        for checkpoint_data in import_data["checkpoints"]:
            record = CheckpointRecord(**checkpoint_data)
            self.checkpoint_records[record.id] = record
        
        # 导入状态版本
        if "state_versions" in import_data:
            self.state_versions[session_id] = [
                StateVersion(**version_data) for version_data in import_data["state_versions"]
            ]
        
        # 保存更改
        self._save_checkpoint_records()
        
        return session_id
