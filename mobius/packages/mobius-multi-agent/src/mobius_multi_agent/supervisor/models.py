"""Pydantic models for supervisor agent state management."""

from typing import Dict, List, Optional, Any, Literal, Union, Set
from enum import Enum
from datetime import datetime
import uuid
import networkx as nx

from pydantic import BaseModel, Field, ConfigDict, field_validator, ValidationInfo, model_validator
from langchain_core.messages import BaseMessage


class TaskStatus(str, Enum):
    """任务执行状态枚举"""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PlanStatus(str, Enum):
    """计划状态枚举"""

    DRAFT = "draft"
    APPROVED = "approved"
    EXECUTING = "executing"
    COMPLETED = "completed"
    NEEDS_REVISION = "needs_revision"


class WorkflowPhase(str, Enum):
    """工作流阶段枚举"""

    PLANNING = "planning"
    EXECUTING = "executing"
    EVALUATING = "evaluating"
    USER_REVIEW = "user_review"
    COMPLETED = "completed"
    FAILED = "failed"


class FeedbackType(str, Enum):
    """用户反馈类型枚举"""

    APPROVE = "approve"
    MODIFY = "modify"
    REJECT = "reject"
    CONTINUE = "continue"
    STOP = "stop"


class DependencyType(str, Enum):
    """依赖关系类型枚举"""

    SEQUENTIAL = "sequential"  # 顺序依赖：必须等待前置任务完成
    CONDITIONAL = "conditional"  # 条件依赖：基于前置任务结果决定是否执行
    RESOURCE = "resource"  # 资源依赖：需要前置任务释放的资源
    DATA = "data"  # 数据依赖：需要前置任务的输出数据


class DependencyCondition(BaseModel):
    """依赖条件定义"""

    model_config = ConfigDict(validate_assignment=True)

    condition_type: Literal["success", "failure", "result_match", "custom"] = Field(
        ..., description="条件类型"
    )
    expected_value: Optional[Any] = Field(default=None, description="期望值")
    condition_expression: Optional[str] = Field(
        default=None, description="自定义条件表达式"
    )

    @field_validator("condition_expression")
    @classmethod
    def validate_custom_condition(cls, v: Optional[str], info: ValidationInfo) -> Optional[str]:
        """验证自定义条件表达式"""
        if info.data.get("condition_type") == "custom" and not v:
            raise ValueError("自定义条件类型必须提供条件表达式")
        return v


class TaskDependency(BaseModel):
    """任务依赖关系定义"""

    model_config = ConfigDict(validate_assignment=True, use_enum_values=True)

    task_id: str = Field(..., description="依赖的任务ID")
    dependency_type: DependencyType = Field(
        default=DependencyType.SEQUENTIAL, description="依赖类型"
    )
    condition: Optional[DependencyCondition] = Field(
        default=None, description="依赖条件"
    )
    timeout_seconds: Optional[int] = Field(
        default=None, ge=0, description="等待超时时间（秒）"
    )
    required: bool = Field(default=True, description="是否为必需依赖")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="依赖元数据")


class SubTask(BaseModel):
    """子任务数据模型

    设计原因：
    - 使用Pydantic提供自动验证和序列化
    - Field提供字段级别的文档和验证
    - 支持任务间的依赖关系和优先级管理
    """

    model_config = ConfigDict(
        str_strip_whitespace=True, validate_assignment=True, use_enum_values=True
    )

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="任务唯一标识"
    )
    description: str = Field(..., min_length=1, max_length=1000, description="任务描述")
    agent_type: str = Field(..., min_length=1, description="执行此任务的agent类型")
    agent_input: Dict[str, Any] = Field(
        default_factory=dict, description="传递给agent的具体输入参数"
    )
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务执行结果")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    dependencies: List[TaskDependency] = Field(
        default_factory=list, description="任务依赖关系列表"
    )
    # 保持向后兼容的简单依赖列表
    simple_dependencies: List[str] = Field(
        default_factory=list, description="简单依赖的任务ID列表（向后兼容）"
    )
    priority: int = Field(default=1, ge=1, le=10, description="优先级，1最高")
    estimated_duration: Optional[int] = Field(
        default=None, ge=0, description="预估执行时间（秒）"
    )
    actual_duration: Optional[int] = Field(
        default=None, ge=0, description="实际执行时间（秒）"
    )
    metadata: Dict[str, Any] = Field(default_factory=dict, description="任务元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    @field_validator("dependencies")
    @classmethod
    def validate_dependencies(cls, v: List[TaskDependency], info: ValidationInfo) -> List[TaskDependency]:
        """验证依赖关系不能包含自己"""
        task_id = info.data.get("id")
        if task_id:
            for dep in v:
                if dep.task_id == task_id:
                    raise ValueError("任务不能依赖自己")
        return v

    @field_validator("simple_dependencies")
    @classmethod
    def validate_simple_dependencies(cls, v: List[str], info: ValidationInfo) -> List[str]:
        """验证简单依赖关系不能包含自己"""
        if "id" in info.data and info.data["id"] in v:
            raise ValueError("任务不能依赖自己")
        return v

    def get_all_dependency_ids(self) -> List[str]:
        """获取所有依赖的任务ID"""
        dep_ids = [dep.task_id for dep in self.dependencies]
        dep_ids.extend(self.simple_dependencies)
        return list(set(dep_ids))  # 去重

    def has_dependency(self, task_id: str) -> bool:
        """检查是否依赖指定任务"""
        return task_id in self.get_all_dependency_ids()

    def get_dependency_by_task_id(self, task_id: str) -> Optional[TaskDependency]:
        """根据任务ID获取依赖关系"""
        for dep in self.dependencies:
            if dep.task_id == task_id:
                return dep
        return None

    def update_status(
        self, status: TaskStatus, error_message: Optional[str] = None
    ) -> None:
        """更新任务状态"""
        self.status = status
        self.error_message = error_message
        self.updated_at = datetime.now()


class ExecutionPlan(BaseModel):
    """执行计划数据模型

    设计原因：
    - 支持计划版本管理和历史追踪
    - 自动计算总预估时间
    - 验证执行顺序的合法性
    """

    model_config = ConfigDict(validate_assignment=True, use_enum_values=True)

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="计划唯一标识"
    )
    version: int = Field(default=1, ge=1, description="计划版本号")
    description: str = Field(..., min_length=1, max_length=2000, description="计划描述")
    subtasks: List[SubTask] = Field(default_factory=list, description="子任务列表")
    status: PlanStatus = Field(default=PlanStatus.DRAFT, description="计划状态")
    execution_order: List[str] = Field(
        default_factory=list, description="子任务ID的执行顺序"
    )
    user_modifications: List[Dict[str, Any]] = Field(
        default_factory=list, description="用户修改历史"
    )
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    estimated_total_duration: Optional[int] = Field(
        default=None, ge=0, description="预估总时间"
    )

    @field_validator("execution_order")
    @classmethod
    def validate_execution_order(cls, v: List[str], info: ValidationInfo) -> List[str]:
        """验证执行顺序包含所有子任务"""
        if "subtasks" in info.data and info.data["subtasks"]:
            task_ids = {task.id for task in info.data["subtasks"]}
            order_ids = set(v)
            if task_ids != order_ids:
                raise ValueError("执行顺序必须包含所有子任务ID")
        return v

    def calculate_estimated_duration(self) -> int:
        """计算预估总时间"""
        total = sum(task.estimated_duration or 0 for task in self.subtasks)
        self.estimated_total_duration = total
        return total

    def validate_dag(self) -> bool:
        """验证任务依赖关系是否构成有效的DAG（无环图）"""
        try:
            # 创建有向图
            graph = {}
            for task in self.subtasks:
                graph[task.id] = task.get_all_dependency_ids()

            # 使用拓扑排序检测环
            visited = set()
            rec_stack = set()

            def has_cycle(node: str) -> bool:
                if node in rec_stack:
                    return True
                if node in visited:
                    return False

                visited.add(node)
                rec_stack.add(node)

                for neighbor in graph.get(node, []):
                    if neighbor in graph and has_cycle(neighbor):
                        return True

                rec_stack.remove(node)
                return False

            # 检查所有节点
            for task_id in graph:
                if task_id not in visited:
                    if has_cycle(task_id):
                        return False

            return True
        except Exception:
            return False

    def get_topological_order(self) -> List[str]:
        """获取任务的拓扑排序顺序"""
        graph: Dict[str, List[str]] = {}
        in_degree: Dict[str, int] = {}

        # 初始化图和入度
        for task in self.subtasks:
            task_id = task.id
            graph[task_id] = []
            in_degree[task_id] = 0

        # 构建图
        for task in self.subtasks:
            for dep_id in task.get_all_dependency_ids():
                if dep_id in graph:
                    graph[dep_id].append(task.id)
                    in_degree[task.id] += 1

        # Kahn算法进行拓扑排序
        queue = [task_id for task_id, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return result if len(result) == len(self.subtasks) else []

    def get_ready_tasks(self) -> List[SubTask]:
        """获取可以执行的任务（依赖已满足）"""
        completed_ids = {
            task.id for task in self.subtasks if task.status == TaskStatus.COMPLETED
        }
        failed_ids = {
            task.id for task in self.subtasks if task.status == TaskStatus.FAILED
        }

        ready_tasks = []
        for task in self.subtasks:
            if task.status == TaskStatus.PENDING:
                # 检查所有依赖是否满足
                dependencies_satisfied = True

                for dep in task.dependencies:
                    if not self._is_dependency_satisfied(dep, completed_ids, failed_ids):
                        dependencies_satisfied = False
                        break

                # 检查简单依赖
                for dep_id in task.simple_dependencies:
                    if dep_id not in completed_ids:
                        dependencies_satisfied = False
                        break

                if dependencies_satisfied:
                    ready_tasks.append(task)

        return sorted(ready_tasks, key=lambda x: x.priority)

    def _is_dependency_satisfied(self, dep: TaskDependency, completed_ids: set[str], failed_ids: set[str]) -> bool:
        """检查单个依赖是否满足"""
        task_id = dep.task_id

        # 如果依赖的任务失败且为必需依赖，则不满足
        if task_id in failed_ids and dep.required:
            return False

        # 如果依赖的任务未完成
        if task_id not in completed_ids:
            return not dep.required  # 非必需依赖可以跳过

        # 检查条件依赖
        if dep.condition:
            return self._evaluate_dependency_condition(dep, task_id)

        return True

    def _evaluate_dependency_condition(self, dep: TaskDependency, task_id: str) -> bool:
        """评估依赖条件"""
        condition = dep.condition
        if not condition:
            return True

        # 找到依赖的任务
        dep_task = next((t for t in self.subtasks if t.id == task_id), None)
        if not dep_task:
            return False

        if condition.condition_type == "success":
            return dep_task.status == TaskStatus.COMPLETED
        elif condition.condition_type == "failure":
            return dep_task.status == TaskStatus.FAILED
        elif condition.condition_type == "result_match":
            if dep_task.result and condition.expected_value is not None:
                return dep_task.result.get("result") == condition.expected_value
        elif condition.condition_type == "custom":
            # 这里可以实现自定义条件表达式的评估
            # 为了安全起见，暂时返回True
            return True

        return True


class AgentResult(BaseModel):
    """智能体执行结果模型

    设计原因：
    - 标准化不同agent的返回格式
    - 包含执行过程的详细信息便于调试
    - 支持置信度评估
    """

    model_config = ConfigDict(validate_assignment=True)

    agent_type: str = Field(..., description="智能体类型")
    task_id: str = Field(..., description="关联的任务ID")
    success: bool = Field(..., description="执行是否成功")
    data: Dict[str, Any] = Field(default_factory=dict, description="结果数据")
    confidence_score: float = Field(
        default=0.0, ge=0.0, le=1.0, description="结果置信度"
    )
    execution_time: float = Field(default=0.0, ge=0.0, description="执行时间（秒）")
    error_details: Optional[str] = Field(default=None, description="错误详情")
    intermediate_steps: List[Dict[str, Any]] = Field(
        default_factory=list, description="中间步骤"
    )
    timestamp: datetime = Field(default_factory=datetime.now, description="执行时间戳")

    @field_validator("confidence_score")
    @classmethod
    def validate_confidence_with_success(cls, v: float, info: ValidationInfo) -> float:
        """验证失败任务的置信度应该较低"""
        if "success" in info.data and not info.data["success"] and v > 0.5:
            raise ValueError("失败任务的置信度不应该超过0.5")
        return v


class UserFeedback(BaseModel):
    """用户反馈数据模型

    设计原因：
    - 结构化用户输入，便于处理
    - 支持不同类型的反馈操作
    - 记录决策理由便于学习改进
    """

    model_config = ConfigDict(
        str_strip_whitespace=True, validate_assignment=True, use_enum_values=True
    )

    feedback_type: FeedbackType = Field(..., description="反馈类型")
    content: str = Field(default="", description="反馈内容")
    modifications: Optional[Dict[str, Any]] = Field(
        default=None, description="修改内容"
    )
    reasoning: Optional[str] = Field(default=None, description="决策理由")
    timestamp: datetime = Field(default_factory=datetime.now, description="反馈时间")

    @field_validator("modifications")
    @classmethod
    def validate_modifications_for_modify(
        cls, v: Optional[Dict[str, Any]], info: ValidationInfo
    ) -> Optional[Dict[str, Any]]:
        """验证修改类型的反馈必须包含修改内容"""
        if (
            "feedback_type" in info.data
            and info.data["feedback_type"] == FeedbackType.MODIFY
            and not v
        ):
            raise ValueError("修改类型的反馈必须包含具体的修改内容")
        return v


class QualityMetric(BaseModel):
    """质量评估指标"""

    model_config = ConfigDict(validate_assignment=True)

    name: str = Field(..., description="指标名称")
    value: float = Field(..., ge=0.0, le=1.0, description="指标值（0-1）")
    weight: float = Field(default=1.0, ge=0.0, description="权重")
    description: Optional[str] = Field(default=None, description="指标描述")
    threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="阈值")


class PerformanceMetrics(BaseModel):
    """性能指标集合"""

    model_config = ConfigDict(validate_assignment=True)

    # 基础指标
    total_tasks: int = Field(default=0, ge=0, description="总任务数")
    completed_tasks: int = Field(default=0, ge=0, description="已完成任务数")
    failed_tasks: int = Field(default=0, ge=0, description="失败任务数")
    cancelled_tasks: int = Field(default=0, ge=0, description="取消任务数")

    # 时间指标
    total_execution_time: float = Field(default=0.0, ge=0.0, description="总执行时间（秒）")
    average_task_time: float = Field(default=0.0, ge=0.0, description="平均任务时间（秒）")
    planning_time: float = Field(default=0.0, ge=0.0, description="计划生成时间（秒）")

    # 质量指标
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="成功率")
    completion_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="完成率")
    efficiency_score: float = Field(default=0.0, ge=0.0, le=1.0, description="效率分数")
    quality_score: float = Field(default=0.0, ge=0.0, le=1.0, description="质量分数")

    # 详细质量指标
    quality_metrics: List[QualityMetric] = Field(default_factory=list, description="详细质量指标")

    # 迭代指标
    iterations_used: int = Field(default=0, ge=0, description="使用的迭代次数")
    max_iterations: int = Field(default=5, ge=1, description="最大迭代次数")

    # 资源使用指标
    agent_utilization: Dict[str, float] = Field(default_factory=dict, description="代理利用率")
    tool_usage_count: Dict[str, int] = Field(default_factory=dict, description="工具使用次数")

    # 错误统计
    error_count: int = Field(default=0, ge=0, description="错误次数")
    retry_count: int = Field(default=0, ge=0, description="重试次数")

    # 时间戳
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    end_time: Optional[datetime] = Field(default=None, description="结束时间")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")

    def calculate_derived_metrics(self) -> None:
        """计算派生指标"""
        # 计算成功率
        if self.total_tasks > 0:
            self.success_rate = self.completed_tasks / self.total_tasks
            self.completion_rate = (self.completed_tasks + self.failed_tasks) / self.total_tasks

        # 计算平均任务时间
        if self.completed_tasks > 0 and self.total_execution_time > 0:
            self.average_task_time = self.total_execution_time / self.completed_tasks

        # 计算效率分数（基于时间和成功率）
        time_efficiency = 1.0
        if self.average_task_time > 0:
            # 假设理想任务时间为30秒
            ideal_time = 30.0
            time_efficiency = min(1.0, ideal_time / self.average_task_time)

        self.efficiency_score = (self.success_rate * 0.7 + time_efficiency * 0.3)

        # 计算综合质量分数
        if self.quality_metrics:
            weighted_sum = sum(metric.value * metric.weight for metric in self.quality_metrics)
            total_weight = sum(metric.weight for metric in self.quality_metrics)
            self.quality_score = weighted_sum / total_weight if total_weight > 0 else 0.0
        else:
            # 基于基础指标计算质量分数
            self.quality_score = (
                self.success_rate * 0.4 +
                self.completion_rate * 0.3 +
                self.efficiency_score * 0.3
            )

        self.last_updated = datetime.now()

    def add_quality_metric(self, name: str, value: float, weight: float = 1.0,
                          description: Optional[str] = None, threshold: Optional[float] = None) -> None:
        """添加质量指标"""
        metric = QualityMetric(
            name=name,
            value=value,
            weight=weight,
            description=description,
            threshold=threshold
        )
        self.quality_metrics.append(metric)

    def get_quality_summary(self) -> Dict[str, Any]:
        """获取质量评估摘要"""
        return {
            "overall_quality": self.quality_score,
            "success_rate": self.success_rate,
            "completion_rate": self.completion_rate,
            "efficiency_score": self.efficiency_score,
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "average_task_time": self.average_task_time,
            "quality_metrics": [metric.model_dump() for metric in self.quality_metrics],
            "recommendation": self._get_recommendation()
        }

    def _get_recommendation(self) -> str:
        """基于指标生成建议"""
        if self.quality_score >= 0.9:
            return "优秀：任务执行质量很高"
        elif self.quality_score >= 0.8:
            return "良好：任务执行质量较好"
        elif self.quality_score >= 0.7:
            return "一般：任务执行质量中等，建议优化"
        elif self.quality_score >= 0.6:
            return "较差：任务执行质量偏低，需要改进"
        else:
            return "差：任务执行质量很低，需要重新规划"


class ConversationEntry(BaseModel):
    """对话记录条目"""

    model_config = ConfigDict(validate_assignment=True)

    role: Literal["user", "assistant", "system"] = Field(..., description="角色")
    content: str = Field(..., description="内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SupervisorState(BaseModel):
    """Supervisor agent 的状态管理模型

    设计原因：
    - 集中管理整个workflow的状态
    - 使用Pydantic确保数据一致性
    - 支持状态序列化和持久化
    """

    model_config = ConfigDict(
        validate_assignment=True,
        arbitrary_types_allowed=True,  # 允许LangChain消息类型
        use_enum_values=True,
    )

    # 核心业务状态
    original_request: str = Field("", description="用户的原始请求")
    current_plan: Optional[ExecutionPlan] = Field(
        default=None, description="当前执行计划"
    )
    agent_results: List[AgentResult] = Field(
        default_factory=list, description="智能体执行结果"
    )
    user_feedback: Optional[UserFeedback] = Field(default=None, description="用户反馈")

    # 执行控制
    current_iteration: int = Field(default=0, ge=0, description="当前迭代次数")
    max_iterations: int = Field(default=5, ge=1, le=20, description="最大迭代次数")
    current_phase: WorkflowPhase = Field(
        default=WorkflowPhase.PLANNING, description="当前阶段"
    )

    # 交互历史
    conversation_history: List[ConversationEntry] = Field(
        default_factory=list, description="对话历史"
    )
    plan_history: List[ExecutionPlan] = Field(
        default_factory=list, description="计划历史"
    )

    # 系统状态
    available_agents: List[str] = Field(
        default_factory=list, description="可用的智能体列表"
    )
    performance_metrics: PerformanceMetrics = Field(
        default_factory=PerformanceMetrics, description="性能指标"
    )

    # 错误处理
    last_error: Optional[str] = Field(default=None, description="最后一个错误")
    fallback_triggered: bool = Field(default=False, description="是否触发兜底机制")

    # LangChain 消息（用于与LLM交互）
    messages: List[BaseMessage] = Field(default_factory=list, description="消息历史")

    def add_conversation_entry(
        self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """添加对话记录"""
        entry = ConversationEntry(
            role=role,  # type: ignore[arg-type]
            content=content,
            metadata=metadata or {},
        )
        self.conversation_history.append(entry)

    def get_completed_tasks(self) -> List[SubTask]:
        """获取已完成的任务"""
        if not self.current_plan:
            return []
        return [
            task
            for task in self.current_plan.subtasks
            if task.status == TaskStatus.COMPLETED
        ]

    def get_pending_tasks(self) -> List[SubTask]:
        """获取待执行的任务"""
        if not self.current_plan:
            return []
        return [
            task
            for task in self.current_plan.subtasks
            if task.status == TaskStatus.PENDING
        ]

    def get_failed_tasks(self) -> List[SubTask]:
        """获取失败的任务"""
        if not self.current_plan:
            return []
        return [
            task
            for task in self.current_plan.subtasks
            if task.status == TaskStatus.FAILED
        ]

    def is_max_iterations_reached(self) -> bool:
        """检查是否达到最大迭代次数"""
        return self.current_iteration >= self.max_iterations

    def get_overall_progress(self) -> float:
        """计算整体进度（0-1）"""
        if not self.current_plan or not self.current_plan.subtasks:
            return 0.0

        completed = len(self.get_completed_tasks())
        total = len(self.current_plan.subtasks)
        return completed / total if total > 0 else 0.0

    def get_success_rate(self) -> float:
        """计算成功率（排除失败任务）"""
        if not self.current_plan or not self.current_plan.subtasks:
            return 0.0

        completed = len(self.get_completed_tasks())
        failed = len(self.get_failed_tasks())
        total = len(self.current_plan.subtasks)

        if total == 0:
            return 0.0

        return completed / (total - failed) if (total - failed) > 0 else 0.0

    def update_performance_metrics(self) -> None:
        """更新性能指标"""
        # 更新基础指标
        self.performance_metrics.total_tasks = (
            len(self.current_plan.subtasks) if self.current_plan else 0
        )
        self.performance_metrics.completed_tasks = len(self.get_completed_tasks())
        self.performance_metrics.failed_tasks = len(self.get_failed_tasks())
        self.performance_metrics.iterations_used = self.current_iteration
        self.performance_metrics.max_iterations = self.max_iterations

        # 计算执行时间
        if self.agent_results:
            self.performance_metrics.total_execution_time = sum(
                result.execution_time for result in self.agent_results
            )

        # 计算代理利用率
        agent_usage = {}
        for result in self.agent_results:
            agent_type = result.agent_type
            agent_usage[agent_type] = agent_usage.get(agent_type, 0) + 1

        if agent_usage:
            total_usage = sum(agent_usage.values())
            self.performance_metrics.agent_utilization = {
                agent: count / total_usage for agent, count in agent_usage.items()
            }

        # 计算错误统计
        self.performance_metrics.error_count = sum(
            1 for result in self.agent_results if not result.success
        )

        # 计算派生指标
        self.performance_metrics.calculate_derived_metrics()

    def add_quality_metric(self, name: str, value: float, weight: float = 1.0,
                          description: Optional[str] = None, threshold: Optional[float] = None) -> None:
        """添加质量评估指标"""
        self.performance_metrics.add_quality_metric(name, value, weight, description, threshold)

    def get_evaluation_summary(self) -> Dict[str, Any]:
        """获取完整的评估摘要"""
        return {
            "performance_summary": self.performance_metrics.get_quality_summary(),
            "task_breakdown": {
                "total": self.performance_metrics.total_tasks,
                "completed": self.performance_metrics.completed_tasks,
                "failed": self.performance_metrics.failed_tasks,
                "pending": len(self.get_pending_tasks()),
            },
            "execution_details": {
                "total_time": self.performance_metrics.total_execution_time,
                "average_time": self.performance_metrics.average_task_time,
                "iterations_used": self.performance_metrics.iterations_used,
                "max_iterations": self.performance_metrics.max_iterations,
            },
            "agent_performance": self.performance_metrics.agent_utilization,
            "current_phase": self.current_phase,
            "last_error": self.last_error,
        }


# 用于LangGraph的状态类型别名
SupervisorGraphState = SupervisorState
