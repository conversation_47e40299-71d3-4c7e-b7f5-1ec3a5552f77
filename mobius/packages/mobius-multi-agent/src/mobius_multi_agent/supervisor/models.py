"""Pydantic models for supervisor agent state management."""

from typing import Dict, List, Optional, Any, Literal, Union
from enum import Enum
from datetime import datetime
import uuid

from pydantic import BaseModel, Field, ConfigDict, field_validator, ValidationInfo
from langchain_core.messages import BaseMessage


class TaskStatus(str, Enum):
    """任务执行状态枚举"""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PlanStatus(str, Enum):
    """计划状态枚举"""

    DRAFT = "draft"
    APPROVED = "approved"
    EXECUTING = "executing"
    COMPLETED = "completed"
    NEEDS_REVISION = "needs_revision"


class WorkflowPhase(str, Enum):
    """工作流阶段枚举"""

    PLANNING = "planning"
    EXECUTING = "executing"
    EVALUATING = "evaluating"
    USER_REVIEW = "user_review"
    COMPLETED = "completed"
    FAILED = "failed"


class FeedbackType(str, Enum):
    """用户反馈类型枚举"""

    APPROVE = "approve"
    MODIFY = "modify"
    REJECT = "reject"
    CONTINUE = "continue"
    STOP = "stop"


class SubTask(BaseModel):
    """子任务数据模型

    设计原因：
    - 使用Pydantic提供自动验证和序列化
    - Field提供字段级别的文档和验证
    - 支持任务间的依赖关系和优先级管理
    """

    model_config = ConfigDict(
        str_strip_whitespace=True, validate_assignment=True, use_enum_values=True
    )

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="任务唯一标识"
    )
    description: str = Field(..., min_length=1, max_length=1000, description="任务描述")
    agent_type: str = Field(..., min_length=1, description="执行此任务的agent类型")
    agent_input: Dict[str, Any] = Field(
        default_factory=dict, description="传递给agent的具体输入参数"
    )
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务执行结果")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    dependencies: List[str] = Field(
        default_factory=list, description="依赖的其他子任务ID"
    )
    priority: int = Field(default=1, ge=1, le=10, description="优先级，1最高")
    estimated_duration: Optional[int] = Field(
        default=None, ge=0, description="预估执行时间（秒）"
    )
    actual_duration: Optional[int] = Field(
        default=None, ge=0, description="实际执行时间（秒）"
    )
    metadata: Dict[str, Any] = Field(default_factory=dict, description="任务元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    @field_validator("dependencies")
    @classmethod
    def validate_dependencies(cls, v: List[str], info: ValidationInfo) -> List[str]:
        """验证依赖关系不能包含自己"""
        if "id" in info.data and info.data["id"] in v:
            raise ValueError("任务不能依赖自己")
        return v

    def update_status(
        self, status: TaskStatus, error_message: Optional[str] = None
    ) -> None:
        """更新任务状态"""
        self.status = status
        self.error_message = error_message
        self.updated_at = datetime.now()


class ExecutionPlan(BaseModel):
    """执行计划数据模型

    设计原因：
    - 支持计划版本管理和历史追踪
    - 自动计算总预估时间
    - 验证执行顺序的合法性
    """

    model_config = ConfigDict(validate_assignment=True, use_enum_values=True)

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="计划唯一标识"
    )
    version: int = Field(default=1, ge=1, description="计划版本号")
    description: str = Field(..., min_length=1, max_length=2000, description="计划描述")
    subtasks: List[SubTask] = Field(default_factory=list, description="子任务列表")
    status: PlanStatus = Field(default=PlanStatus.DRAFT, description="计划状态")
    execution_order: List[str] = Field(
        default_factory=list, description="子任务ID的执行顺序"
    )
    user_modifications: List[Dict[str, Any]] = Field(
        default_factory=list, description="用户修改历史"
    )
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    estimated_total_duration: Optional[int] = Field(
        default=None, ge=0, description="预估总时间"
    )

    @field_validator("execution_order")
    @classmethod
    def validate_execution_order(cls, v: List[str], info: ValidationInfo) -> List[str]:
        """验证执行顺序包含所有子任务"""
        if "subtasks" in info.data and info.data["subtasks"]:
            task_ids = {task.id for task in info.data["subtasks"]}
            order_ids = set(v)
            if task_ids != order_ids:
                raise ValueError("执行顺序必须包含所有子任务ID")
        return v

    def calculate_estimated_duration(self) -> int:
        """计算预估总时间"""
        total = sum(task.estimated_duration or 0 for task in self.subtasks)
        self.estimated_total_duration = total
        return total

    def get_ready_tasks(self) -> List[SubTask]:
        """获取可以执行的任务（依赖已满足）"""
        completed_ids = {
            task.id for task in self.subtasks if task.status == TaskStatus.COMPLETED
        }

        ready_tasks = []
        for task in self.subtasks:
            if task.status == TaskStatus.PENDING and all(
                dep_id in completed_ids for dep_id in task.dependencies
            ):
                ready_tasks.append(task)

        return sorted(ready_tasks, key=lambda x: x.priority)


class AgentResult(BaseModel):
    """智能体执行结果模型

    设计原因：
    - 标准化不同agent的返回格式
    - 包含执行过程的详细信息便于调试
    - 支持置信度评估
    """

    model_config = ConfigDict(validate_assignment=True)

    agent_type: str = Field(..., description="智能体类型")
    task_id: str = Field(..., description="关联的任务ID")
    success: bool = Field(..., description="执行是否成功")
    data: Dict[str, Any] = Field(default_factory=dict, description="结果数据")
    confidence_score: float = Field(
        default=0.0, ge=0.0, le=1.0, description="结果置信度"
    )
    execution_time: float = Field(default=0.0, ge=0.0, description="执行时间（秒）")
    error_details: Optional[str] = Field(default=None, description="错误详情")
    intermediate_steps: List[Dict[str, Any]] = Field(
        default_factory=list, description="中间步骤"
    )
    timestamp: datetime = Field(default_factory=datetime.now, description="执行时间戳")

    @field_validator("confidence_score")
    @classmethod
    def validate_confidence_with_success(cls, v: float, info: ValidationInfo) -> float:
        """验证失败任务的置信度应该较低"""
        if "success" in info.data and not info.data["success"] and v > 0.5:
            raise ValueError("失败任务的置信度不应该超过0.5")
        return v


class UserFeedback(BaseModel):
    """用户反馈数据模型

    设计原因：
    - 结构化用户输入，便于处理
    - 支持不同类型的反馈操作
    - 记录决策理由便于学习改进
    """

    model_config = ConfigDict(
        str_strip_whitespace=True, validate_assignment=True, use_enum_values=True
    )

    feedback_type: FeedbackType = Field(..., description="反馈类型")
    content: str = Field(default="", description="反馈内容")
    modifications: Optional[Dict[str, Any]] = Field(
        default=None, description="修改内容"
    )
    reasoning: Optional[str] = Field(default=None, description="决策理由")
    timestamp: datetime = Field(default_factory=datetime.now, description="反馈时间")

    @field_validator("modifications")
    @classmethod
    def validate_modifications_for_modify(
        cls, v: Optional[Dict[str, Any]], info: ValidationInfo
    ) -> Optional[Dict[str, Any]]:
        """验证修改类型的反馈必须包含修改内容"""
        if (
            "feedback_type" in info.data
            and info.data["feedback_type"] == FeedbackType.MODIFY
            and not v
        ):
            raise ValueError("修改类型的反馈必须包含具体的修改内容")
        return v


class ConversationEntry(BaseModel):
    """对话记录条目"""

    model_config = ConfigDict(validate_assignment=True)

    role: Literal["user", "assistant", "system"] = Field(..., description="角色")
    content: str = Field(..., description="内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SupervisorState(BaseModel):
    """Supervisor agent 的状态管理模型

    设计原因：
    - 集中管理整个workflow的状态
    - 使用Pydantic确保数据一致性
    - 支持状态序列化和持久化
    """

    model_config = ConfigDict(
        validate_assignment=True,
        arbitrary_types_allowed=True,  # 允许LangChain消息类型
        use_enum_values=True,
    )

    # 核心业务状态
    original_request: str = Field("", description="用户的原始请求")
    current_plan: Optional[ExecutionPlan] = Field(
        default=None, description="当前执行计划"
    )
    agent_results: List[AgentResult] = Field(
        default_factory=list, description="智能体执行结果"
    )
    user_feedback: Optional[UserFeedback] = Field(default=None, description="用户反馈")

    # 执行控制
    current_iteration: int = Field(default=0, ge=0, description="当前迭代次数")
    max_iterations: int = Field(default=5, ge=1, le=20, description="最大迭代次数")
    current_phase: WorkflowPhase = Field(
        default=WorkflowPhase.PLANNING, description="当前阶段"
    )

    # 交互历史
    conversation_history: List[ConversationEntry] = Field(
        default_factory=list, description="对话历史"
    )
    plan_history: List[ExecutionPlan] = Field(
        default_factory=list, description="计划历史"
    )

    # 系统状态
    available_agents: List[str] = Field(
        default_factory=list, description="可用的智能体列表"
    )
    performance_metrics: Dict[str, Any] = Field(
        default_factory=dict, description="性能指标"
    )

    # 错误处理
    last_error: Optional[str] = Field(default=None, description="最后一个错误")
    fallback_triggered: bool = Field(default=False, description="是否触发兜底机制")

    # LangChain 消息（用于与LLM交互）
    messages: List[BaseMessage] = Field(default_factory=list, description="消息历史")

    def add_conversation_entry(
        self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """添加对话记录"""
        entry = ConversationEntry(
            role=role,  # type: ignore[arg-type]
            content=content,
            metadata=metadata or {},
        )
        self.conversation_history.append(entry)

    def get_completed_tasks(self) -> List[SubTask]:
        """获取已完成的任务"""
        if not self.current_plan:
            return []
        return [
            task
            for task in self.current_plan.subtasks
            if task.status == TaskStatus.COMPLETED
        ]

    def get_pending_tasks(self) -> List[SubTask]:
        """获取待执行的任务"""
        if not self.current_plan:
            return []
        return [
            task
            for task in self.current_plan.subtasks
            if task.status == TaskStatus.PENDING
        ]

    def get_failed_tasks(self) -> List[SubTask]:
        """获取失败的任务"""
        if not self.current_plan:
            return []
        return [
            task
            for task in self.current_plan.subtasks
            if task.status == TaskStatus.FAILED
        ]

    def is_max_iterations_reached(self) -> bool:
        """检查是否达到最大迭代次数"""
        return self.current_iteration >= self.max_iterations

    def get_overall_progress(self) -> float:
        """计算整体进度（0-1）"""
        if not self.current_plan or not self.current_plan.subtasks:
            return 0.0

        completed = len(self.get_completed_tasks())
        total = len(self.current_plan.subtasks)
        return completed / total if total > 0 else 0.0

    def get_success_rate(self) -> float:
        """计算成功率（排除失败任务）"""
        if not self.current_plan or not self.current_plan.subtasks:
            return 0.0

        completed = len(self.get_completed_tasks())
        failed = len(self.get_failed_tasks())
        total = len(self.current_plan.subtasks)

        if total == 0:
            return 0.0

        return completed / (total - failed) if (total - failed) > 0 else 0.0

    def update_performance_metrics(self) -> None:
        """更新性能指标"""
        self.performance_metrics.update(
            {
                "overall_progress": self.get_overall_progress(),
                "success_rate": self.get_success_rate(),
                "total_tasks": (
                    len(self.current_plan.subtasks) if self.current_plan else 0
                ),
                "completed_tasks": len(self.get_completed_tasks()),
                "failed_tasks": len(self.get_failed_tasks()),
                "current_iteration": self.current_iteration,
                "max_iterations": self.max_iterations,
                "last_updated": datetime.now().isoformat(),
            }
        )


# 用于LangGraph的状态类型别名
SupervisorGraphState = SupervisorState
