"""
动态配置管理系统

实现动态增减 agent、MCP tool 和 LLM 配置的管理
"""

import json
import importlib
import inspect
from typing import Dict, Any, Optional, List, Type, Union, Callable
from datetime import datetime
from pathlib import Path
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator
from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_mcp_adapters.client import MultiServerMCPClient

from ..base.agent import BaseAgent


class ConfigType(str, Enum):
    """配置类型枚举"""
    
    AGENT = "agent"
    TOOL = "tool"
    LLM = "llm"
    MCP_SERVER = "mcp_server"
    PROMPT_TEMPLATE = "prompt_template"
    SCENARIO = "scenario"


class ConfigStatus(str, Enum):
    """配置状态枚举"""
    
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    LOADING = "loading"
    DEPRECATED = "deprecated"


class AgentConfig(BaseModel):
    """代理配置"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    name: str = Field(..., description="代理名称")
    class_path: str = Field(..., description="代理类路径")
    description: str = Field(..., description="代理描述")
    capabilities: List[str] = Field(default_factory=list, description="代理能力")
    input_schema: Dict[str, Any] = Field(default_factory=dict, description="输入模式")
    output_schema: Dict[str, Any] = Field(default_factory=dict, description="输出模式")
    config_params: Dict[str, Any] = Field(default_factory=dict, description="配置参数")
    dependencies: List[str] = Field(default_factory=list, description="依赖项")
    version: str = Field(default="1.0", description="版本")
    status: ConfigStatus = Field(default=ConfigStatus.INACTIVE, description="状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class ToolConfig(BaseModel):
    """工具配置"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    name: str = Field(..., description="工具名称")
    tool_type: str = Field(..., description="工具类型")  # function, class, mcp
    source: str = Field(..., description="工具源")  # 函数路径、类路径或MCP服务器
    description: str = Field(..., description="工具描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="工具参数")
    config_params: Dict[str, Any] = Field(default_factory=dict, description="配置参数")
    version: str = Field(default="1.0", description="版本")
    status: ConfigStatus = Field(default=ConfigStatus.INACTIVE, description="状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class LLMConfig(BaseModel):
    """LLM配置"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    name: str = Field(..., description="LLM名称")
    provider: str = Field(..., description="提供商")  # openai, anthropic, etc.
    model_name: str = Field(..., description="模型名称")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    base_url: Optional[str] = Field(default=None, description="基础URL")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="模型参数")
    rate_limits: Dict[str, Any] = Field(default_factory=dict, description="速率限制")
    version: str = Field(default="1.0", description="版本")
    status: ConfigStatus = Field(default=ConfigStatus.INACTIVE, description="状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class MCPServerConfig(BaseModel):
    """MCP服务器配置"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    name: str = Field(..., description="服务器名称")
    command: str = Field(..., description="启动命令")
    args: List[str] = Field(default_factory=list, description="命令参数")
    env: Dict[str, str] = Field(default_factory=dict, description="环境变量")
    working_directory: Optional[str] = Field(default=None, description="工作目录")
    timeout: int = Field(default=30, description="连接超时时间")
    auto_restart: bool = Field(default=True, description="自动重启")
    version: str = Field(default="1.0", description="版本")
    status: ConfigStatus = Field(default=ConfigStatus.INACTIVE, description="状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.config_dir = Path(config_dir) if config_dir else Path("configs")
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置存储
        self.agent_configs: Dict[str, AgentConfig] = {}
        self.tool_configs: Dict[str, ToolConfig] = {}
        self.llm_configs: Dict[str, LLMConfig] = {}
        self.mcp_server_configs: Dict[str, MCPServerConfig] = {}
        
        # 运行时实例
        self.agent_instances: Dict[str, BaseAgent] = {}
        self.tool_instances: Dict[str, BaseTool] = {}
        self.llm_instances: Dict[str, ChatOpenAI] = {}
        self.mcp_clients: Dict[str, MultiServerMCPClient] = {}
        
        # 加载配置
        self._load_configs()
    
    def _load_configs(self) -> None:
        """加载所有配置"""
        config_files = {
            "agents.json": (self.agent_configs, AgentConfig),
            "tools.json": (self.tool_configs, ToolConfig),
            "llms.json": (self.llm_configs, LLMConfig),
            "mcp_servers.json": (self.mcp_server_configs, MCPServerConfig),
        }
        
        for filename, (storage, config_class) in config_files.items():
            config_file = self.config_dir / filename
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        configs_data = json.load(f)
                        for config_data in configs_data:
                            config = config_class(**config_data)
                            storage[config.name] = config
                except Exception as e:
                    print(f"加载配置文件 {filename} 失败: {e}")
    
    def _save_configs(self) -> None:
        """保存所有配置"""
        config_data = {
            "agents.json": list(self.agent_configs.values()),
            "tools.json": list(self.tool_configs.values()),
            "llms.json": list(self.llm_configs.values()),
            "mcp_servers.json": list(self.mcp_server_configs.values()),
        }
        
        for filename, configs in config_data.items():
            config_file = self.config_dir / filename
            try:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump([config.model_dump(mode='json') for config in configs], 
                             f, ensure_ascii=False, indent=2, default=str)
            except Exception as e:
                print(f"保存配置文件 {filename} 失败: {e}")
    
    # Agent 管理
    async def register_agent(self, config: AgentConfig) -> bool:
        """注册代理"""
        try:
            # 动态加载代理类
            agent_instance = await self._load_agent_instance(config)
            if agent_instance:
                self.agent_configs[config.name] = config
                self.agent_instances[config.name] = agent_instance
                config.status = ConfigStatus.ACTIVE
                config.updated_at = datetime.now()
                self._save_configs()
                return True
        except Exception as e:
            config.status = ConfigStatus.ERROR
            print(f"注册代理 {config.name} 失败: {e}")
        
        return False
    
    async def _load_agent_instance(self, config: AgentConfig) -> Optional[BaseAgent]:
        """加载代理实例"""
        try:
            # 解析类路径
            module_path, class_name = config.class_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            agent_class = getattr(module, class_name)
            
            # 创建实例
            agent_instance = agent_class(
                name=config.name,
                description=config.description,
                **config.config_params
            )
            
            return agent_instance
        except Exception as e:
            print(f"加载代理实例失败: {e}")
            return None
    
    def unregister_agent(self, agent_name: str) -> bool:
        """注销代理"""
        if agent_name in self.agent_configs:
            # 更新状态
            self.agent_configs[agent_name].status = ConfigStatus.INACTIVE
            self.agent_configs[agent_name].updated_at = datetime.now()
            
            # 移除实例
            if agent_name in self.agent_instances:
                del self.agent_instances[agent_name]
            
            self._save_configs()
            return True
        return False
    
    def get_agent(self, agent_name: str) -> Optional[BaseAgent]:
        """获取代理实例"""
        return self.agent_instances.get(agent_name)
    
    def list_agents(self, status: Optional[ConfigStatus] = None) -> List[AgentConfig]:
        """列出代理"""
        agents = list(self.agent_configs.values())
        if status:
            agents = [agent for agent in agents if agent.status == status]
        return agents
    
    # Tool 管理
    async def register_tool(self, config: ToolConfig) -> bool:
        """注册工具"""
        try:
            tool_instance = await self._load_tool_instance(config)
            if tool_instance:
                self.tool_configs[config.name] = config
                self.tool_instances[config.name] = tool_instance
                config.status = ConfigStatus.ACTIVE
                config.updated_at = datetime.now()
                self._save_configs()
                return True
        except Exception as e:
            config.status = ConfigStatus.ERROR
            print(f"注册工具 {config.name} 失败: {e}")
        
        return False
    
    async def _load_tool_instance(self, config: ToolConfig) -> Optional[BaseTool]:
        """加载工具实例"""
        try:
            if config.tool_type == "function":
                # 加载函数工具
                module_path, func_name = config.source.rsplit('.', 1)
                module = importlib.import_module(module_path)
                func = getattr(module, func_name)
                
                from langchain_core.tools import tool
                return tool(func)
            
            elif config.tool_type == "class":
                # 加载类工具
                module_path, class_name = config.source.rsplit('.', 1)
                module = importlib.import_module(module_path)
                tool_class = getattr(module, class_name)
                return tool_class(**config.config_params)
            
            elif config.tool_type == "mcp":
                # MCP工具将在MCP客户端中处理
                return None
            
        except Exception as e:
            print(f"加载工具实例失败: {e}")
            return None
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """获取工具实例"""
        return self.tool_instances.get(tool_name)
    
    def list_tools(self, status: Optional[ConfigStatus] = None) -> List[ToolConfig]:
        """列出工具"""
        tools = list(self.tool_configs.values())
        if status:
            tools = [tool for tool in tools if tool.status == status]
        return tools
    
    # LLM 管理
    async def register_llm(self, config: LLMConfig) -> bool:
        """注册LLM"""
        try:
            llm_instance = await self._load_llm_instance(config)
            if llm_instance:
                self.llm_configs[config.name] = config
                self.llm_instances[config.name] = llm_instance
                config.status = ConfigStatus.ACTIVE
                config.updated_at = datetime.now()
                self._save_configs()
                return True
        except Exception as e:
            config.status = ConfigStatus.ERROR
            print(f"注册LLM {config.name} 失败: {e}")
        
        return False
    
    async def _load_llm_instance(self, config: LLMConfig) -> Optional[ChatOpenAI]:
        """加载LLM实例"""
        try:
            if config.provider == "openai":
                llm_params = {
                    "model": config.model_name,
                    **config.parameters
                }
                
                if config.api_key:
                    llm_params["api_key"] = config.api_key
                if config.base_url:
                    llm_params["base_url"] = config.base_url
                
                return ChatOpenAI(**llm_params)
            
            # 可以扩展支持其他提供商
            else:
                print(f"不支持的LLM提供商: {config.provider}")
                return None
                
        except Exception as e:
            print(f"加载LLM实例失败: {e}")
            return None
    
    def get_llm(self, llm_name: str) -> Optional[ChatOpenAI]:
        """获取LLM实例"""
        return self.llm_instances.get(llm_name)
    
    def list_llms(self, status: Optional[ConfigStatus] = None) -> List[LLMConfig]:
        """列出LLM"""
        llms = list(self.llm_configs.values())
        if status:
            llms = [llm for llm in llms if llm.status == status]
        return llms
    
    # MCP Server 管理
    async def register_mcp_server(self, config: MCPServerConfig) -> bool:
        """注册MCP服务器"""
        try:
            # 这里可以实现MCP服务器的启动和连接逻辑
            self.mcp_server_configs[config.name] = config
            config.status = ConfigStatus.ACTIVE
            config.updated_at = datetime.now()
            self._save_configs()
            return True
        except Exception as e:
            config.status = ConfigStatus.ERROR
            print(f"注册MCP服务器 {config.name} 失败: {e}")
            return False
    
    def list_mcp_servers(self, status: Optional[ConfigStatus] = None) -> List[MCPServerConfig]:
        """列出MCP服务器"""
        servers = list(self.mcp_server_configs.values())
        if status:
            servers = [server for server in servers if server.status == status]
        return servers
    
    # 通用方法
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "agents": {
                "total": len(self.agent_configs),
                "active": len([a for a in self.agent_configs.values() if a.status == ConfigStatus.ACTIVE]),
                "inactive": len([a for a in self.agent_configs.values() if a.status == ConfigStatus.INACTIVE]),
                "error": len([a for a in self.agent_configs.values() if a.status == ConfigStatus.ERROR]),
            },
            "tools": {
                "total": len(self.tool_configs),
                "active": len([t for t in self.tool_configs.values() if t.status == ConfigStatus.ACTIVE]),
                "inactive": len([t for t in self.tool_configs.values() if t.status == ConfigStatus.INACTIVE]),
                "error": len([t for t in self.tool_configs.values() if t.status == ConfigStatus.ERROR]),
            },
            "llms": {
                "total": len(self.llm_configs),
                "active": len([l for l in self.llm_configs.values() if l.status == ConfigStatus.ACTIVE]),
                "inactive": len([l for l in self.llm_configs.values() if l.status == ConfigStatus.INACTIVE]),
                "error": len([l for l in self.llm_configs.values() if l.status == ConfigStatus.ERROR]),
            },
            "mcp_servers": {
                "total": len(self.mcp_server_configs),
                "active": len([m for m in self.mcp_server_configs.values() if m.status == ConfigStatus.ACTIVE]),
                "inactive": len([m for m in self.mcp_server_configs.values() if m.status == ConfigStatus.INACTIVE]),
                "error": len([m for m in self.mcp_server_configs.values() if m.status == ConfigStatus.ERROR]),
            }
        }
    
    async def reload_all_configs(self) -> Dict[str, int]:
        """重新加载所有配置"""
        # 清除当前实例
        self.agent_instances.clear()
        self.tool_instances.clear()
        self.llm_instances.clear()
        self.mcp_clients.clear()
        
        # 重新加载配置
        self._load_configs()
        
        # 重新创建实例
        results = {"agents": 0, "tools": 0, "llms": 0, "mcp_servers": 0}
        
        for config in self.agent_configs.values():
            if await self.register_agent(config):
                results["agents"] += 1
        
        for config in self.tool_configs.values():
            if await self.register_tool(config):
                results["tools"] += 1
        
        for config in self.llm_configs.values():
            if await self.register_llm(config):
                results["llms"] += 1
        
        for config in self.mcp_server_configs.values():
            if await self.register_mcp_server(config):
                results["mcp_servers"] += 1
        
        return results
