"""
提示词模板管理系统

支持动态配置、多场景适配和模板继承的提示词管理
"""

import json
import os
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator
from jinja2 import Environment, FileSystemLoader, Template, meta


class PromptType(str, Enum):
    """提示词类型枚举"""
    
    PLAN_GENERATION = "plan_generation"
    TASK_EXECUTION = "task_execution"
    RESULT_EVALUATION = "result_evaluation"
    USER_INTERACTION = "user_interaction"
    ERROR_HANDLING = "error_handling"
    PLAN_REVISION = "plan_revision"
    AGENT_COORDINATION = "agent_coordination"
    QUALITY_ASSESSMENT = "quality_assessment"


class PromptTemplate(BaseModel):
    """提示词模板定义"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    id: str = Field(..., description="模板唯一标识")
    name: str = Field(..., description="模板名称")
    type: PromptType = Field(..., description="模板类型")
    template: str = Field(..., description="模板内容")
    description: Optional[str] = Field(default=None, description="模板描述")
    variables: List[str] = Field(default_factory=list, description="模板变量列表")
    parent_template: Optional[str] = Field(default=None, description="父模板ID（支持继承）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="模板元数据")
    version: str = Field(default="1.0", description="模板版本")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    @field_validator("template")
    @classmethod
    def validate_template_syntax(cls, v: str) -> str:
        """验证模板语法"""
        try:
            # 使用Jinja2验证模板语法
            env = Environment()
            env.parse(v)
            return v
        except Exception as e:
            raise ValueError(f"模板语法错误: {str(e)}")
    
    def extract_variables(self) -> List[str]:
        """提取模板中的变量"""
        env = Environment()
        ast = env.parse(self.template)
        variables = list(meta.find_undeclared_variables(ast))
        self.variables = variables
        return variables
    
    def render(self, context: Dict[str, Any]) -> str:
        """渲染模板"""
        env = Environment()
        template = env.from_string(self.template)
        return template.render(**context)


class PromptScenario(BaseModel):
    """提示词场景配置"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    name: str = Field(..., description="场景名称")
    description: Optional[str] = Field(default=None, description="场景描述")
    template_mappings: Dict[PromptType, str] = Field(
        default_factory=dict, description="场景下的模板映射"
    )
    default_context: Dict[str, Any] = Field(
        default_factory=dict, description="默认上下文变量"
    )
    conditions: Dict[str, Any] = Field(
        default_factory=dict, description="场景激活条件"
    )


class PromptManager:
    """提示词模板管理器"""
    
    def __init__(self, templates_dir: Optional[str] = None):
        self.templates_dir = Path(templates_dir) if templates_dir else Path("prompts")
        self.templates: Dict[str, PromptTemplate] = {}
        self.scenarios: Dict[str, PromptScenario] = {}
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)) if self.templates_dir.exists() else None
        )
        self._load_templates()
        self._load_scenarios()
    
    def _load_templates(self) -> None:
        """从文件系统加载模板"""
        if not self.templates_dir.exists():
            self._create_default_templates()
            return
        
        # 加载JSON格式的模板定义
        templates_file = self.templates_dir / "templates.json"
        if templates_file.exists():
            with open(templates_file, 'r', encoding='utf-8') as f:
                templates_data = json.load(f)
                for template_data in templates_data:
                    template = PromptTemplate(**template_data)
                    self.templates[template.id] = template
        
        # 加载单独的模板文件
        for template_file in self.templates_dir.glob("*.jinja2"):
            template_id = template_file.stem
            if template_id not in self.templates:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    template = PromptTemplate(
                        id=template_id,
                        name=template_id.replace('_', ' ').title(),
                        type=self._infer_template_type(template_id),
                        template=content
                    )
                    template.extract_variables()
                    self.templates[template_id] = template
    
    def _load_scenarios(self) -> None:
        """加载场景配置"""
        scenarios_file = self.templates_dir / "scenarios.json"
        if scenarios_file.exists():
            with open(scenarios_file, 'r', encoding='utf-8') as f:
                scenarios_data = json.load(f)
                for scenario_data in scenarios_data:
                    scenario = PromptScenario(**scenario_data)
                    self.scenarios[scenario.name] = scenario
    
    def _infer_template_type(self, template_id: str) -> PromptType:
        """根据模板ID推断模板类型"""
        type_mapping = {
            "plan": PromptType.PLAN_GENERATION,
            "execute": PromptType.TASK_EXECUTION,
            "evaluate": PromptType.RESULT_EVALUATION,
            "interact": PromptType.USER_INTERACTION,
            "error": PromptType.ERROR_HANDLING,
            "revise": PromptType.PLAN_REVISION,
            "coordinate": PromptType.AGENT_COORDINATION,
            "assess": PromptType.QUALITY_ASSESSMENT,
        }
        
        for key, prompt_type in type_mapping.items():
            if key in template_id.lower():
                return prompt_type
        
        return PromptType.PLAN_GENERATION  # 默认类型
    
    def _create_default_templates(self) -> None:
        """创建默认模板"""
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        default_templates = [
            PromptTemplate(
                id="plan_generation_default",
                name="默认计划生成模板",
                type=PromptType.PLAN_GENERATION,
                template="""
作为多代理系统监督器，请分析用户查询并生成执行计划。

用户查询: {{ user_query }}
可用代理: {{ available_agents }}
可用工具: {{ available_tools }}
{% if context_info %}
上下文信息: {{ context_info }}
{% endif %}

请根据用户查询生成执行计划，包括：
1. 任务分解和依赖关系
2. 代理分配和输入参数
3. 执行顺序和优先级

确保：
- 任务描述清晰具体
- agent_type 使用准确的代理名称
- agent_input 格式符合各代理的要求
- 正确设置任务间的依赖关系
                """.strip()
            ),
            PromptTemplate(
                id="result_evaluation_default",
                name="默认结果评估模板",
                type=PromptType.RESULT_EVALUATION,
                template="""
请评估以下任务执行结果：

完成的任务: {{ completed_tasks }}
失败的任务: {{ failed_tasks }}
性能指标: {{ performance_metrics }}
{% if quality_metrics %}
质量指标: {{ quality_metrics }}
{% endif %}

评估要点：
1. 结果质量和准确性
2. 任务完成度和效率
3. 是否需要进一步处理
4. 改进建议

返回评估结果和建议。
                """.strip()
            )
        ]
        
        for template in default_templates:
            template.extract_variables()
            self.templates[template.id] = template
        
        # 保存到文件
        self._save_templates()
    
    def get_template(self, template_id: str, scenario: Optional[str] = None) -> Optional[PromptTemplate]:
        """获取模板"""
        # 如果指定了场景，优先使用场景中的模板映射
        if scenario and scenario in self.scenarios:
            scenario_obj = self.scenarios[scenario]
            # 根据模板ID推断类型，然后查找场景映射
            template_type = self._infer_template_type(template_id)
            if template_type in scenario_obj.template_mappings:
                mapped_template_id = scenario_obj.template_mappings[template_type]
                if mapped_template_id in self.templates:
                    return self.templates[mapped_template_id]
        
        return self.templates.get(template_id)
    
    def render_template(self, template_id: str, context: Dict[str, Any], 
                       scenario: Optional[str] = None) -> str:
        """渲染模板"""
        template = self.get_template(template_id, scenario)
        if not template:
            raise ValueError(f"模板 {template_id} 不存在")
        
        # 合并场景默认上下文
        final_context = {}
        if scenario and scenario in self.scenarios:
            final_context.update(self.scenarios[scenario].default_context)
        final_context.update(context)
        
        return template.render(final_context)
    
    def add_template(self, template: PromptTemplate) -> None:
        """添加模板"""
        template.extract_variables()
        template.updated_at = datetime.now()
        self.templates[template.id] = template
        self._save_templates()
    
    def update_template(self, template_id: str, **kwargs) -> None:
        """更新模板"""
        if template_id not in self.templates:
            raise ValueError(f"模板 {template_id} 不存在")
        
        template = self.templates[template_id]
        for key, value in kwargs.items():
            if hasattr(template, key):
                setattr(template, key, value)
        
        template.updated_at = datetime.now()
        if 'template' in kwargs:
            template.extract_variables()
        
        self._save_templates()
    
    def delete_template(self, template_id: str) -> None:
        """删除模板"""
        if template_id in self.templates:
            del self.templates[template_id]
            self._save_templates()
    
    def add_scenario(self, scenario: PromptScenario) -> None:
        """添加场景"""
        self.scenarios[scenario.name] = scenario
        self._save_scenarios()
    
    def get_scenario(self, scenario_name: str) -> Optional[PromptScenario]:
        """获取场景"""
        return self.scenarios.get(scenario_name)
    
    def list_templates(self, template_type: Optional[PromptType] = None) -> List[PromptTemplate]:
        """列出模板"""
        templates = list(self.templates.values())
        if template_type:
            templates = [t for t in templates if t.type == template_type]
        return sorted(templates, key=lambda x: x.name)
    
    def list_scenarios(self) -> List[PromptScenario]:
        """列出场景"""
        return list(self.scenarios.values())
    
    def _save_templates(self) -> None:
        """保存模板到文件"""
        templates_data = [template.model_dump() for template in self.templates.values()]
        templates_file = self.templates_dir / "templates.json"
        with open(templates_file, 'w', encoding='utf-8') as f:
            json.dump(templates_data, f, ensure_ascii=False, indent=2, default=str)
    
    def _save_scenarios(self) -> None:
        """保存场景到文件"""
        scenarios_data = [scenario.model_dump() for scenario in self.scenarios.values()]
        scenarios_file = self.templates_dir / "scenarios.json"
        with open(scenarios_file, 'w', encoding='utf-8') as f:
            json.dump(scenarios_data, f, ensure_ascii=False, indent=2, default=str)
