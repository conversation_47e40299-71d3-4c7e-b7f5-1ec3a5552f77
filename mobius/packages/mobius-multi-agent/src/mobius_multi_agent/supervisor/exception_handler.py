"""
异常处理和重试机制

提供完整的异常捕获、重试策略、兜底机制和中断处理
"""

import asyncio
import logging
import traceback
from typing import Dict, Any, Optional, List, Callable, Union, Type
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass

from pydantic import BaseModel, Field, ConfigDict


class ExceptionType(str, Enum):
    """异常类型枚举"""
    
    AGENT_ERROR = "agent_error"  # 代理执行错误
    TIMEOUT_ERROR = "timeout_error"  # 超时错误
    DEPENDENCY_ERROR = "dependency_error"  # 依赖错误
    VALIDATION_ERROR = "validation_error"  # 验证错误
    RESOURCE_ERROR = "resource_error"  # 资源错误
    NETWORK_ERROR = "network_error"  # 网络错误
    PERMISSION_ERROR = "permission_error"  # 权限错误
    DATA_ERROR = "data_error"  # 数据错误
    SYSTEM_ERROR = "system_error"  # 系统错误
    UNKNOWN_ERROR = "unknown_error"  # 未知错误


class RetryStrategy(str, Enum):
    """重试策略枚举"""
    
    IMMEDIATE = "immediate"  # 立即重试
    LINEAR_BACKOFF = "linear_backoff"  # 线性退避
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    FIXED_DELAY = "fixed_delay"  # 固定延迟
    NO_RETRY = "no_retry"  # 不重试


class FallbackStrategy(str, Enum):
    """兜底策略枚举"""
    
    SKIP_TASK = "skip_task"  # 跳过任务
    SIMPLIFIED_APPROACH = "simplified_approach"  # 简化方法
    ALTERNATIVE_AGENT = "alternative_agent"  # 替代代理
    MANUAL_INTERVENTION = "manual_intervention"  # 人工干预
    ABORT_PLAN = "abort_plan"  # 中止计划


@dataclass
class RetryConfig:
    """重试配置"""
    
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0  # 基础延迟（秒）
    max_delay: float = 60.0  # 最大延迟（秒）
    backoff_factor: float = 2.0  # 退避因子
    jitter: bool = True  # 是否添加随机抖动


class ExceptionRecord(BaseModel):
    """异常记录"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    id: str = Field(..., description="异常记录ID")
    exception_type: ExceptionType = Field(..., description="异常类型")
    task_id: Optional[str] = Field(default=None, description="关联任务ID")
    agent_type: Optional[str] = Field(default=None, description="关联代理类型")
    error_message: str = Field(..., description="错误消息")
    stack_trace: Optional[str] = Field(default=None, description="堆栈跟踪")
    context: Dict[str, Any] = Field(default_factory=dict, description="异常上下文")
    timestamp: datetime = Field(default_factory=datetime.now, description="发生时间")
    retry_count: int = Field(default=0, description="重试次数")
    resolved: bool = Field(default=False, description="是否已解决")
    resolution_method: Optional[str] = Field(default=None, description="解决方法")


class InterruptRequest(BaseModel):
    """中断请求"""
    
    model_config = ConfigDict(validate_assignment=True)
    
    task_id: str = Field(..., description="任务ID")
    reason: str = Field(..., description="中断原因")
    requires_user_input: bool = Field(default=False, description="是否需要用户输入")
    user_prompt: Optional[str] = Field(default=None, description="用户提示信息")
    timeout_seconds: Optional[int] = Field(default=None, description="超时时间")
    context: Dict[str, Any] = Field(default_factory=dict, description="中断上下文")
    timestamp: datetime = Field(default_factory=datetime.now, description="中断时间")


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.exception_records: List[ExceptionRecord] = []
        self.retry_configs: Dict[ExceptionType, RetryConfig] = self._get_default_retry_configs()
        self.fallback_handlers: Dict[ExceptionType, Callable] = {}
        self.interrupt_requests: List[InterruptRequest] = []
    
    def _get_default_retry_configs(self) -> Dict[ExceptionType, RetryConfig]:
        """获取默认重试配置"""
        return {
            ExceptionType.AGENT_ERROR: RetryConfig(max_attempts=3, strategy=RetryStrategy.EXPONENTIAL_BACKOFF),
            ExceptionType.TIMEOUT_ERROR: RetryConfig(max_attempts=2, strategy=RetryStrategy.LINEAR_BACKOFF),
            ExceptionType.NETWORK_ERROR: RetryConfig(max_attempts=5, strategy=RetryStrategy.EXPONENTIAL_BACKOFF),
            ExceptionType.RESOURCE_ERROR: RetryConfig(max_attempts=3, strategy=RetryStrategy.FIXED_DELAY, base_delay=5.0),
            ExceptionType.VALIDATION_ERROR: RetryConfig(max_attempts=1, strategy=RetryStrategy.NO_RETRY),
            ExceptionType.PERMISSION_ERROR: RetryConfig(max_attempts=1, strategy=RetryStrategy.NO_RETRY),
            ExceptionType.SYSTEM_ERROR: RetryConfig(max_attempts=2, strategy=RetryStrategy.EXPONENTIAL_BACKOFF),
            ExceptionType.UNKNOWN_ERROR: RetryConfig(max_attempts=2, strategy=RetryStrategy.EXPONENTIAL_BACKOFF),
        }
    
    def classify_exception(self, exception: Exception, context: Optional[Dict[str, Any]] = None) -> ExceptionType:
        """分类异常"""
        exception_name = type(exception).__name__.lower()
        error_message = str(exception).lower()
        
        # 基于异常类型分类
        if "timeout" in exception_name or "timeout" in error_message:
            return ExceptionType.TIMEOUT_ERROR
        elif "permission" in exception_name or "permission" in error_message:
            return ExceptionType.PERMISSION_ERROR
        elif "validation" in exception_name or "validation" in error_message:
            return ExceptionType.VALIDATION_ERROR
        elif "network" in exception_name or "connection" in error_message:
            return ExceptionType.NETWORK_ERROR
        elif "resource" in exception_name or "memory" in error_message:
            return ExceptionType.RESOURCE_ERROR
        elif context and context.get("agent_type"):
            return ExceptionType.AGENT_ERROR
        else:
            return ExceptionType.UNKNOWN_ERROR
    
    async def handle_exception(self, exception: Exception, task_id: Optional[str] = None,
                             agent_type: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理异常"""
        exception_type = self.classify_exception(exception, context)
        
        # 记录异常
        record = ExceptionRecord(
            id=f"exc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.exception_records)}",
            exception_type=exception_type,
            task_id=task_id,
            agent_type=agent_type,
            error_message=str(exception),
            stack_trace=traceback.format_exc(),
            context=context or {}
        )
        self.exception_records.append(record)
        
        self.logger.error(f"异常处理: {exception_type} - {str(exception)}", exc_info=True)
        
        # 获取重试配置
        retry_config = self.retry_configs.get(exception_type, RetryConfig())
        
        # 决定处理策略
        if record.retry_count < retry_config.max_attempts and retry_config.strategy != RetryStrategy.NO_RETRY:
            # 执行重试
            delay = self._calculate_retry_delay(retry_config, record.retry_count)
            return {
                "action": "retry",
                "delay": delay,
                "retry_count": record.retry_count + 1,
                "exception_record": record
            }
        else:
            # 执行兜底策略
            fallback_action = await self._execute_fallback_strategy(exception_type, record)
            return {
                "action": "fallback",
                "fallback_strategy": fallback_action,
                "exception_record": record
            }
    
    def _calculate_retry_delay(self, config: RetryConfig, attempt: int) -> float:
        """计算重试延迟"""
        if config.strategy == RetryStrategy.IMMEDIATE:
            return 0.0
        elif config.strategy == RetryStrategy.FIXED_DELAY:
            delay = config.base_delay
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * (attempt + 1)
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_factor ** attempt)
        else:
            delay = config.base_delay
        
        # 应用最大延迟限制
        delay = min(delay, config.max_delay)
        
        # 添加随机抖动
        if config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay
    
    async def _execute_fallback_strategy(self, exception_type: ExceptionType, record: ExceptionRecord) -> str:
        """执行兜底策略"""
        # 检查是否有自定义兜底处理器
        if exception_type in self.fallback_handlers:
            try:
                result = await self.fallback_handlers[exception_type](record)
                return result
            except Exception as e:
                self.logger.error(f"兜底处理器执行失败: {e}")
        
        # 默认兜底策略
        if exception_type in [ExceptionType.VALIDATION_ERROR, ExceptionType.PERMISSION_ERROR]:
            return FallbackStrategy.MANUAL_INTERVENTION
        elif exception_type == ExceptionType.TIMEOUT_ERROR:
            return FallbackStrategy.SIMPLIFIED_APPROACH
        elif exception_type == ExceptionType.AGENT_ERROR:
            return FallbackStrategy.ALTERNATIVE_AGENT
        elif exception_type == ExceptionType.RESOURCE_ERROR:
            return FallbackStrategy.SKIP_TASK
        else:
            return FallbackStrategy.MANUAL_INTERVENTION
    
    def register_fallback_handler(self, exception_type: ExceptionType, handler: Callable) -> None:
        """注册兜底处理器"""
        self.fallback_handlers[exception_type] = handler
    
    def create_interrupt_request(self, task_id: str, reason: str, requires_user_input: bool = False,
                               user_prompt: Optional[str] = None, timeout_seconds: Optional[int] = None,
                               context: Optional[Dict[str, Any]] = None) -> InterruptRequest:
        """创建中断请求"""
        interrupt = InterruptRequest(
            task_id=task_id,
            reason=reason,
            requires_user_input=requires_user_input,
            user_prompt=user_prompt,
            timeout_seconds=timeout_seconds,
            context=context or {}
        )
        self.interrupt_requests.append(interrupt)
        return interrupt
    
    def get_exception_statistics(self) -> Dict[str, Any]:
        """获取异常统计"""
        total_exceptions = len(self.exception_records)
        if total_exceptions == 0:
            return {"total": 0, "by_type": {}, "resolution_rate": 0.0}
        
        by_type = {}
        resolved_count = 0
        
        for record in self.exception_records:
            exception_type = record.exception_type
            by_type[exception_type] = by_type.get(exception_type, 0) + 1
            if record.resolved:
                resolved_count += 1
        
        return {
            "total": total_exceptions,
            "by_type": by_type,
            "resolution_rate": resolved_count / total_exceptions,
            "recent_exceptions": [
                {
                    "type": record.exception_type,
                    "message": record.error_message,
                    "timestamp": record.timestamp,
                    "resolved": record.resolved
                }
                for record in self.exception_records[-10:]  # 最近10个异常
            ]
        }
    
    def mark_exception_resolved(self, exception_id: str, resolution_method: str) -> bool:
        """标记异常已解决"""
        for record in self.exception_records:
            if record.id == exception_id:
                record.resolved = True
                record.resolution_method = resolution_method
                return True
        return False
    
    def clear_old_records(self, days: int = 7) -> int:
        """清理旧的异常记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        initial_count = len(self.exception_records)
        
        self.exception_records = [
            record for record in self.exception_records
            if record.timestamp > cutoff_date
        ]
        
        return initial_count - len(self.exception_records)
