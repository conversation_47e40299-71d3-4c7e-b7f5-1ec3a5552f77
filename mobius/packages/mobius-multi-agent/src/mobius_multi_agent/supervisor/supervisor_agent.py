import asyncio
import logging
from typing import Callable, Dict, Any, List, Optional, Union, Sequence
from datetime import datetime

from langchain_core.runnables.utils import Input as RInputT, Output as ROutputT
from langchain_core.language_models import BaseChatModel
from langchain_core.tools import BaseTool, Tool
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_mcp_adapters.client import MultiServerMCPClient  # type: ignore

from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode

from langgraph.types import Command, interrupt
from langchain_core.runnables.base import RunnableLike
from langchain_core.runnables import RunnableLambda # Added import
from pydantic import BaseModel
from mobius_multi_agent.base.agent import BaseAgent
from mobius_multi_agent.supervisor.models import (
    SupervisorGraphState,
    ExecutionPlan,
    SubTask,
    TaskStatus,
    PlanStatus,
    WorkflowPhase,
    FeedbackType,
    UserFeedback,
    AgentResult,
    ConversationEntry,
)
try:
    from mobius_multi_agent.supervisor.prompt_manager import PromptManager, PromptType
    from mobius_multi_agent.supervisor.exception_handler import ExceptionHandler, ExceptionType
    from mobius_multi_agent.supervisor.checkpoint_manager import CheckpointManager, CheckpointType
    from mobius_multi_agent.supervisor.config_manager import ConfigManager
except ImportError:
    # 如果新模块不存在，使用占位符
    PromptManager = None
    PromptType = None
    ExceptionHandler = None
    ExceptionType = None
    CheckpointManager = None
    CheckpointType = None
    ConfigManager = None

logger = logging.getLogger(__name__)


class SupervisorContext(BaseModel):
    """监督器上下文"""

    sub_agents: Dict[str, Any]
    mcp_tools: Dict[str, Tool]
    prompt_templates: Dict[str, str]
    config: Dict[str, Any]


class SupervisorInput(BaseModel):
    """监督器输入"""

    query: str
    user_id: Optional[str]
    session_id: Optional[str]
    config_overrides: Optional[Dict[str, Any]]


class SupervisorOutput(BaseModel):
    """监督器输出"""

    result: Dict[str, Any]
    execution_summary: Dict[str, Any]
    tasks_completed: List[Dict[str, Any]]
    status: str


class SupervisorAgent(
    BaseAgent[
        SupervisorGraphState, SupervisorContext, SupervisorInput, SupervisorOutput
    ]
):
    """监督器代理实现"""

    def __init__(
        self,
        agents: list[BaseAgent[Any, Any, Any, Any]],
        *,
        name: str = "supervisor",
        description: str = "多代理系统监督器",
        tools: Optional[
            Union[
                Sequence[Union[BaseTool, Callable[..., Any], dict[str, Any]]], ToolNode
            ]
        ] = None,
        llm: Optional[ChatOpenAI] = None,
        mcp_client: Optional[MultiServerMCPClient] = None,
        max_iterations: int = 5,
        pre_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
        post_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
        config_dir: Optional[str] = None,
        prompts_dir: Optional[str] = None,
        checkpoints_dir: Optional[str] = None,
    ):
        super().__init__(
            name,
            description,
            llm=llm,
            tools=tools,
            mcp_client=mcp_client,
            max_iterations=max_iterations,
            pre_model_hook=pre_model_hook,
            post_model_hook=post_model_hook,
        )
        self.sub_agents = {agent.name: agent for agent in agents}

        # 初始化管理器
        self.prompt_manager = PromptManager(prompts_dir) if PromptManager else None
        self.exception_handler = ExceptionHandler() if ExceptionHandler else None
        self.checkpoint_manager = CheckpointManager(checkpoints_dir) if CheckpointManager else None
        self.config_manager = ConfigManager(config_dir) if ConfigManager else None

        # 加载提示模板（向后兼容）
        self._load_prompt_templates()

    def _load_prompt_templates(self) -> None:
        """加载提示模板"""
        self.prompt_templates = {
            "plan_generation": """
                            作为多代理系统监督器，请分析用户查询并生成执行计划。

                            用户查询: {user_query}
                            可用代理: {available_agents}
                            可用工具: {available_tools}

                            请根据用户查询生成执行计划。

                            对于 UnitAgent，输入格式应为：
                            {{"task": "具体的转换任务描述", "tool": "", "value": 数值, "result": 0.0}}

                            对于 MathAgent，输入格式应为：
                            {{"expression": "数学表达式", "user_input": 可选的用户输入值}}

                            请确保：
                            1. 任务描述清晰具体
                            2. agent_type 使用准确的代理名称
                            3. agent_input 格式符合各代理的要求
                            4. 正确设置任务间的依赖关系
                            """,
            "result_evaluation": """
                            请评估以下任务执行结果：

                            完成的任务: {completed_tasks}
                            失败的任务: {failed_tasks}
                            最终结果: {final_result}

                            评估要点：
                            1. 结果质量
                            2. 完成度
                            3. 是否需要进一步处理

                            返回评估结果和建议。""",
        }

    def build_nodes(
        self,
        builder: StateGraph[
            SupervisorGraphState, SupervisorContext, SupervisorInput, SupervisorOutput
        ],
    ) -> StateGraph[
        SupervisorGraphState, SupervisorContext, SupervisorInput, SupervisorOutput
    ]:
        """构建节点"""
        # 定义初始状态映射器，将 SupervisorInput 转换为 SupervisorGraphState
        def create_initial_state_with_config(x, config=None):
            """支持配置的初始状态映射器"""
            # 尝试从配置中获取初始输入
            initial_input = None
            if config and isinstance(config, dict):
                # 先尝试从根级别获取
                initial_input = config.get('initial_input')
                # 如果没有，尝试从 configurable 中获取
                if not initial_input and 'configurable' in config:
                    configurable = config['configurable']
                    if isinstance(configurable, dict):
                        initial_input = configurable.get('initial_input')
            
            # 如果配置中有初始输入，使用它
            if initial_input and hasattr(initial_input, 'query'):
                return SupervisorGraphState(
                    original_request=initial_input.query,
                    available_agents=[agent.name for agent in self.sub_agents.values()],
                    current_phase=WorkflowPhase.PLANNING,
                    conversation_history=[ConversationEntry(role="user", content=initial_input.query)],
                )
            
            # 否则使用默认逻辑创建初始状态
            if hasattr(x, 'query'):
                return SupervisorGraphState(
                    original_request=x.query,
                    available_agents=[agent.name for agent in self.sub_agents.values()],
                    current_phase=WorkflowPhase.PLANNING,
                    conversation_history=[ConversationEntry(role="user", content=x.query)],
                )
            else:
                return SupervisorGraphState(
                    original_request=str(x),
                    available_agents=[agent.name for agent in self.sub_agents.values()],
                    current_phase=WorkflowPhase.PLANNING,
                    conversation_history=[ConversationEntry(role="user", content=str(x))],
                )
        
        initial_state_mapper = RunnableLambda(create_initial_state_with_config).with_types(
            input_type=SupervisorInput, output_type=SupervisorGraphState
        )

        builder.add_node("initial_state_mapper", initial_state_mapper)
        builder.add_node("plan_generation", self._plan_generation)
        builder.add_node("task_execution", self._task_execution)
        builder.add_node("result_evaluator", self._result_evaluator)
        # 新增：用户交互节点，用于中断
        builder.add_node("user_interaction", self._user_interaction_node)
        return builder

    def build_edges(
        self,
        builder: StateGraph[
            SupervisorGraphState, SupervisorContext, SupervisorInput, SupervisorOutput
        ],
    ) -> StateGraph[
        SupervisorGraphState, SupervisorContext, SupervisorInput, SupervisorOutput
    ]:
        """构建边"""
        builder.add_edge(START, "initial_state_mapper")
        builder.add_edge("initial_state_mapper", "plan_generation")
        builder.add_edge("plan_generation", "task_execution")

        # 添加条件边，实现中断
        builder.add_conditional_edges(
            "task_execution",
            self._decide_next_step,
            {
                "continue": "task_execution",
                "evaluate": "result_evaluator",
                "interrupt": "user_interaction",  # 指向中断节点
            },
        )
        # 用户交互后，返回任务执行
        builder.add_edge("user_interaction", "task_execution")
        builder.add_edge("result_evaluator", END)

        return builder

    def _decide_next_step(self, state: SupervisorGraphState) -> str:
        """根据当前状态决定下一步操作"""
        # 检查当前阶段
        if state.current_phase == WorkflowPhase.FAILED:
            return "evaluate"
        
        if state.current_phase == WorkflowPhase.EVALUATING:
            return "evaluate"
        
        # 检查是否有需要用户输入的标记
        for result in state.agent_results:
            if result.data.get("needs_user_input"):
                state.current_phase = WorkflowPhase.USER_REVIEW
                return "interrupt"
        
        # 检查是否还有待执行的任务
        if state.get_pending_tasks():
            return "continue"
        
        # 没有待执行的任务，进入评估阶段
        return "evaluate"

    def _user_interaction_node(
        self, state: SupervisorGraphState
    ) -> SupervisorGraphState:
        """用户交互节点，触发中断"""
        return interrupt(value=state)

    async def _plan_generation(self, state: SupervisorGraphState) -> Dict[str, Any]:
        """计划生成节点"""
        logger.info("开始生成执行计划")

        try:
            # 创建检查点
            if self.checkpoint_manager and CheckpointType:
                await self.checkpoint_manager.create_checkpoint(
                    session_id=getattr(state, 'session_id', 'default'),
                    state=state,
                    checkpoint_type=CheckpointType.PLAN_GENERATED,
                    description="开始计划生成"
                )

            # 使用提示词管理器获取模板
            if self.prompt_manager:
                prompt_content = self.prompt_manager.render_template(
                    "plan_generation_default",
                    {
                        "user_query": state.original_request,
                        "available_agents": state.available_agents,
                        "available_tools": self._get_available_tools(),
                        "agent_capabilities": self._get_agent_capabilities(),
                        "agent_input_schemas": self._get_agent_input_schemas(),
                    }
                )
            else:
                # 向后兼容：使用原有模板
                prompt = ChatPromptTemplate.from_template(
                    self.prompt_templates["plan_generation"]
                )
                prompt_content = prompt.format(
                    user_query=state.original_request,
                    available_agents=state.available_agents,
                    available_tools=self._get_available_tools(),
                )

            if not self.llm:
                raise ValueError("LLM 未配置")

            # 直接使用文本解析方式，更兼容不同API
            enhanced_prompt = prompt_content + """

请以JSON格式返回执行计划，格式如下：
{
    "description": "计划描述",
    "subtasks": [
        {
            "id": "task_1",
            "description": "任务描述",
            "agent_type": "代理类型",
            "agent_input": {"参数": "值"},
            "priority": 1,
            "estimated_duration": 30
        }
    ]
}

请确保返回的是有效的JSON格式。
"""

            try:
                # 添加超时处理
                response = await asyncio.wait_for(
                    self.llm.ainvoke([HumanMessage(content=enhanced_prompt)]),
                    timeout=30.0  # 30秒超时
                )

                # 解析文本响应
                import json
                import re

                response_text = str(response.content) if response.content else ""
                logger.debug(f"LLM响应: {response_text[:200]}...")

                # 尝试提取JSON部分
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    try:
                        plan_data = json.loads(json_match.group())

                        # 确保有execution_order字段
                        if 'execution_order' not in plan_data:
                            if 'subtasks' in plan_data:
                                plan_data['execution_order'] = [task.get('id', f'task_{i}') for i, task in enumerate(plan_data['subtasks'])]
                            else:
                                plan_data['execution_order'] = []

                        execution_plan = ExecutionPlan.model_validate(plan_data)
                        logger.info("成功解析LLM生成的执行计划")
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.error(f"JSON解析失败: {e}")
                        execution_plan = self._create_fallback_plan(state.original_request)
                else:
                    logger.error("无法从响应中提取JSON，使用兜底计划")
                    execution_plan = self._create_fallback_plan(state.original_request)

            except asyncio.TimeoutError:
                logger.error("LLM调用超时，使用兜底计划")
                execution_plan = self._create_fallback_plan(state.original_request)
            except Exception as llm_error:
                logger.error(f"LLM调用失败: {llm_error}")
                execution_plan = self._create_fallback_plan(state.original_request)

            # 验证DAG
            if not execution_plan.validate_dag():
                raise ValueError("生成的计划包含循环依赖，无法执行")

            logger.info(f"生成了包含 {len(execution_plan.subtasks)} 个任务的执行计划")

            # Update state with the new plan and add to history
            state.current_plan = execution_plan
            state.plan_history.append(execution_plan)
            state.current_phase = WorkflowPhase.EXECUTING
            state.add_conversation_entry(
                "assistant",
                f"已生成执行计划，包含 {len(execution_plan.subtasks)} 个任务",
            )

            # 创建计划生成完成检查点
            if self.checkpoint_manager and CheckpointType:
                await self.checkpoint_manager.create_checkpoint(
                    session_id=getattr(state, 'session_id', 'default'),
                    state=state,
                    checkpoint_type=CheckpointType.PLAN_GENERATED,
                    description=f"计划生成完成，包含 {len(execution_plan.subtasks)} 个任务"
                )

            return state.model_dump()

        except Exception as e:
            logger.error(f"计划生成失败: {e}")

            # 异常处理
            if self.exception_handler:
                exception_result = await self.exception_handler.handle_exception(
                    e, context={"phase": "plan_generation", "state": state.model_dump()}
                )
                logger.info(f"异常处理结果: {exception_result}")

            state.last_error = f"计划生成失败: {str(e)}"
            state.current_phase = WorkflowPhase.FAILED
            return state.model_dump()

    async def _task_execution(self, state: SupervisorGraphState) -> Dict[str, Any]:
        """任务执行节点"""
        logger.info("开始执行任务")

        if not state.current_plan:
            state.last_error = "没有可执行的计划"
            state.current_phase = WorkflowPhase.FAILED
            return state.model_dump()

        plan = state.current_plan

        # Get tasks that are ready to be executed
        ready_tasks = plan.get_ready_tasks()

        if not ready_tasks:
            # All tasks are either completed, failed, or not yet ready
            # Check if there are any pending tasks that are not ready (due to dependencies)
            pending_tasks = state.get_pending_tasks()
            if not pending_tasks:
                # All tasks are done or failed, move to evaluation
                state.current_phase = WorkflowPhase.EVALUATING
                return state.model_dump()
            else:
                # Some tasks are pending but not ready, this indicates a potential deadlock or issue
                state.last_error = (
                    "存在未完成的任务，但没有可执行的任务（可能存在依赖问题或死锁）"
                )
                state.current_phase = WorkflowPhase.FAILED
                return state.model_dump()

        # Execute ready tasks (limiting to 2 for concurrency example, can be adjusted)
        for subtask in ready_tasks[:2]:  # Limit concurrency
            subtask.update_status(TaskStatus.IN_PROGRESS)
            try:
                agent_result = await self._execute_single_task(subtask)
                state.agent_results.append(agent_result)

                if agent_result.success:
                    subtask.result = agent_result.data  # Set result directly
                    subtask.update_status(TaskStatus.COMPLETED)
                else:
                    subtask.update_status(
                        TaskStatus.FAILED, error_message=agent_result.error_details
                    )
            except Exception as e:
                logger.error(f"任务 {subtask.id} 执行失败: {e}")
                subtask.update_status(TaskStatus.FAILED, error_message=str(e))
                state.last_error = str(e)

        # Update performance metrics after task execution
        state.update_performance_metrics()

        # Check if all tasks are completed or failed
        if not state.get_pending_tasks():
            state.current_phase = WorkflowPhase.EVALUATING
        else:
            state.current_phase = WorkflowPhase.EXECUTING  # Continue execution

        return state.model_dump()

    async def _execute_single_task(self, subtask: SubTask) -> AgentResult:
        """执行单个任务"""
        logger.info(f"执行任务: {subtask.description} (Agent: {subtask.agent_type})")

        start_time = datetime.now()

        try:
            agent = self.sub_agents.get(subtask.agent_type)
            if agent:
                # 获取子代理的图
                sub_agent_graph = agent.get_graph()

                # 使用 subtask.agent_input 调用子代理
                logger.info(
                    f"调用子代理 {subtask.agent_type}，输入: {subtask.agent_input}"
                )
                
                # 为子代理提供适当的 context
                context = None
                if subtask.agent_type == "UnitAgent":
                    context = {"llm": self.llm}
                elif subtask.agent_type == "MathAgent":
                    context = {"llm": self.llm}
                
                if context:
                    sub_agent_final_state = await sub_agent_graph.ainvoke(
                        subtask.agent_input, context=context
                    )
                else:
                    sub_agent_final_state = await sub_agent_graph.ainvoke(
                        subtask.agent_input
                    )

                # 从子代理的最终状态中提取结果
                result_data: Dict[str, Any]
                if isinstance(sub_agent_final_state, BaseModel):
                    raw_result = sub_agent_final_state.model_dump().get(
                        "result", sub_agent_final_state.model_dump()
                    )
                elif isinstance(sub_agent_final_state, dict):
                    raw_result = sub_agent_final_state.get(
                        "result", sub_agent_final_state
                    )
                else:
                    raw_result = sub_agent_final_state
                
                # 确保 result_data 是字典格式
                if isinstance(raw_result, dict):
                    result_data = raw_result
                else:
                    result_data = {"result": raw_result}

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                return AgentResult(
                    agent_type=subtask.agent_type,
                    task_id=subtask.id,
                    success=True,
                    data=result_data,
                    execution_time=duration,
                )
            else:
                # 如果找不到特定代理，则回退到LLM
                result_data = await self._execute_with_llm(subtask)

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                return AgentResult(
                    agent_type="llm_fallback",
                    task_id=subtask.id,
                    success=True,
                    data=result_data,
                    execution_time=duration,
                )

        except Exception as e:
            logger.error(f"任务 {subtask.id} 执行失败: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            return AgentResult(
                agent_type=subtask.agent_type,
                task_id=subtask.id,
                success=False,
                error_details=str(e),
                execution_time=duration,
            )

    async def _execute_with_llm(self, subtask: SubTask) -> Dict[str, Any]:
        """使用LLM执行任务"""
        prompt = f"请处理以下任务: {subtask.description}"
        if not self.llm:
            raise ValueError("LLM 未配置")
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return {"response": response.content}

    async def _result_evaluator(self, state: SupervisorGraphState) -> Dict[str, Any]:
        """结果评估节点"""
        logger.info("评估执行结果")

        # Use state's built-in methods for tasks
        completed_tasks = state.get_completed_tasks()
        failed_tasks = state.get_failed_tasks()

        # Aggregate results from agent_results
        final_result_summary = {
            "total_agent_results": len(state.agent_results),
            "successful_agent_results": sum(
                1 for r in state.agent_results if r.success
            ),
            "failed_agent_results": sum(
                1 for r in state.agent_results if not r.success
            ),
            "overall_progress": state.get_overall_progress(),
            "success_rate": state.get_success_rate(),
            "completed_tasks_count": len(completed_tasks),
            "failed_tasks_count": len(failed_tasks),
            "details": [
                r.model_dump() for r in state.agent_results
            ],  # Include all agent results
        }

        # Determine overall quality score based on state metrics
        quality_score = (
            state.get_success_rate() * 0.8 + state.get_overall_progress() * 0.2
        )  # Example weighting

        evaluation_result = {
            "quality_score": min(quality_score, 1.0),
            "success_rate": state.get_success_rate(),
            "total_tasks": (
                len(state.current_plan.subtasks) if state.current_plan else 0
            ),
            "completed_tasks": len(completed_tasks),
            "failed_tasks": len(failed_tasks),
            "recommendation": "完成" if quality_score >= 0.8 else "需要改进",
            "final_summary": final_result_summary,
        }

        logger.info(f"结果评估完成，质量分数: {quality_score:.2f}")

        # 更新性能指标
        for key, value in evaluation_result.items():
            if hasattr(state.performance_metrics, key):
                setattr(state.performance_metrics, key, value)
        state.current_phase = WorkflowPhase.COMPLETED
        state.add_conversation_entry(
            "assistant", f"任务已完成，评估结果：{evaluation_result['recommendation']}"
        )

        return state.model_dump()

    def _get_available_tools(self) -> list[str]:
        """获取可用工具列表"""
        if not self.tools:
            return []

        tool_names = []
        if isinstance(self.tools, ToolNode):
            tool_names = list(self.tools.tools_by_name.keys())
        elif isinstance(self.tools, (list, tuple)):
            for tool in self.tools:
                if isinstance(tool, BaseTool):  # Check if it's a BaseTool instance
                    tool_names.append(tool.name)
                elif callable(tool):
                    tool_names.append(getattr(tool, "__name__", str(tool)))
                elif isinstance(tool, dict) and "name" in tool:
                    tool_names.append(tool["name"])

        return tool_names

    def _get_agent_capabilities(self) -> Dict[str, str]:
        """获取代理能力描述"""
        capabilities = {}
        for agent_name, agent in self.sub_agents.items():
            capabilities[agent_name] = getattr(agent, 'description', '通用任务处理')
        return capabilities

    def _get_agent_input_schemas(self) -> Dict[str, str]:
        """获取代理输入模式"""
        schemas = {}
        for agent_name in self.sub_agents.keys():
            if agent_name == "UnitAgent":
                schemas[agent_name] = '{"task": "转换任务描述", "tool": "工具名称", "value": 数值, "result": 0.0}'
            elif agent_name == "MathAgent":
                schemas[agent_name] = '{"expression": "数学表达式", "user_input": "可选用户输入"}'
            else:
                schemas[agent_name] = '{"task": "任务描述", "input": "输入数据"}'
        return schemas

    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            "agents": {
                "total": len(self.sub_agents),
                "available": list(self.sub_agents.keys()),
            },
            "tools": {
                "total": len(self._get_available_tools()),
                "available": self._get_available_tools(),
            },
            "managers": {
                "prompt_manager": self.prompt_manager is not None,
                "exception_handler": self.exception_handler is not None,
                "checkpoint_manager": self.checkpoint_manager is not None,
                "config_manager": self.config_manager is not None,
            }
        }

        # 添加配置管理器状态
        if self.config_manager:
            status["config_summary"] = self.config_manager.get_config_summary()

        # 添加异常统计
        if self.exception_handler:
            status["exception_statistics"] = self.exception_handler.get_exception_statistics()

        return status

    async def reload_configurations(self) -> Dict[str, Any]:
        """重新加载配置"""
        results = {"success": False, "details": {}}

        if self.config_manager:
            try:
                reload_results = await self.config_manager.reload_all_configs()
                results["success"] = True
                results["details"] = reload_results
            except Exception as e:
                results["error"] = str(e)
        else:
            results["error"] = "配置管理器未初始化"

        return results

    def _create_fallback_plan(self, user_request: str) -> ExecutionPlan:
        """创建兜底执行计划"""
        logger.info("创建兜底执行计划")

        # 分析用户请求，创建简单的任务分解
        subtasks = []

        # 检查是否包含单位转换
        if any(keyword in user_request.lower() for keyword in ['公里', '英里', '摄氏', '华氏', '转换', 'km', 'mile']):
            subtasks.append(SubTask(
                id="unit_conversion_task",
                description="单位转换任务",
                agent_type="UnitAgent",
                agent_input={"task": "convert", "tool": "auto_detect", "value": 100, "result": 0.0},
                priority=1,
                estimated_duration=30
            ))

        # 检查是否包含数学计算
        if any(keyword in user_request.lower() for keyword in ['计算', '加', '减', '乘', '除', '+', '-', '*', '/', '数学']):
            subtasks.append(SubTask(
                id="math_calculation_task",
                description="数学计算任务",
                agent_type="MathAgent",
                agent_input={"expression": "x", "user_input": "请根据上下文计算"},
                priority=2,
                estimated_duration=30
            ))

        # 如果没有识别出特定任务，创建一个通用任务
        if not subtasks:
            subtasks.append(SubTask(
                id="general_task",
                description="通用处理任务",
                agent_type="MathAgent",  # 默认使用数学代理
                agent_input={"expression": "1", "user_input": user_request},
                priority=1,
                estimated_duration=30
            ))

        return ExecutionPlan(
            description=f"兜底执行计划：{user_request[:50]}...",
            subtasks=subtasks,
            execution_order=[task.id for task in subtasks]
        )
