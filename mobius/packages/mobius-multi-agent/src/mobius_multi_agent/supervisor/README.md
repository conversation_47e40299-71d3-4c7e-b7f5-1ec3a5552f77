# SupervisorAgent - 多代理系统监督器

## 概述

`SupervisorAgent` 是一个多代理系统的监督器，负责协调和管理多个子代理的执行。它实现了完整的任务规划、执行、监控和评估流程。

## 主要特性

- **智能任务规划**: 基于用户查询自动生成执行计划
- **多代理协调**: 支持协调不同类型的子代理（单位转换、数学计算等）
- **用户交互**: 支持用户审核计划和中断处理
- **异常处理**: 完善的错误处理和重试机制
- **结果评估**: 自动评估执行结果质量
- **兜底机制**: 在失败时提供友好的回退处理

## 工作流程

监督器遵循以下工作流程：

1. **输入处理**: 将用户输入转换为内部状态
2. **计划生成**: 分析需求并生成任务执行计划
3. **用户审核**: 等待用户确认或修改计划
4. **任务执行**: 调用相应的子代理执行任务
5. **结果评估**: 检查执行结果的质量和完成度
6. **迭代优化**: 根据评估结果决定是否需要改进

## 使用示例

### 基本用法

```python
from mobius_multi_agent.supervisor.supervisor_agent import SupervisorAgent
from mobius_multi_agent.unit_agent import UnitAgent
from mobius_multi_agent.math_agent import MathAgent
from langchain_openai import ChatOpenAI

# 创建 LLM
llm = ChatOpenAI(model="gpt-4")

# 创建子代理
unit_agent = UnitAgent(
    name="unit_converter",
    description="单位转换代理",
    llm=llm
)

math_agent = MathAgent(
    name="math_calculator",
    description="数学计算代理",
    llm=llm
)

# 创建监督器
supervisor = SupervisorAgent(
    agents=[unit_agent, math_agent],
    name="multi_agent_supervisor",
    description="多代理系统监督器",
    llm=llm,
    max_iterations=3
)

# 获取执行图
graph = supervisor.get_graph()

# 执行任务
result = await graph.ainvoke({
    "query": "请将10公里转换为英里",
    "user_id": None,
    "session_id": None,
    "config_overrides": None
})
```

### 支持的任务类型

#### 1. 单位转换任务
```python
# 距离转换
"请将10公里转换为英里"

# 温度转换  
"请将25摄氏度转换为华氏度"
```

#### 2. 数学计算任务
```python
# 简单计算
"请计算二加三乘四的结果"

# 复杂表达式
"请计算（五加三）的二次方"
```

#### 3. 混合任务
```python
# 多步骤任务
"请将5公里转换为英里，然后计算结果乘以二的值"
```

## 配置选项

### SupervisorAgent 参数

- `agents`: 子代理列表
- `name`: 监督器名称（默认: "supervisor"）
- `description`: 监督器描述
- `llm`: 语言模型实例
- `max_iterations`: 最大迭代次数（默认: 3）
- `tools`: 可选的工具集合
- `mcp_client`: MCP 客户端（可选）

### 状态管理

监督器维护以下状态信息：

- `user_query`: 用户原始查询
- `execution_plan`: 当前执行计划
- `active_tasks`: 正在执行的任务
- `completed_tasks`: 已完成的任务
- `failed_tasks`: 失败的任务
- `iteration_count`: 当前迭代次数
- `final_result`: 最终结果

## 错误处理

### 异常类型

1. **计划生成失败**: 回退到基于查询的智能任务分解
2. **子代理执行失败**: 自动重试或切换到备用策略
3. **达到迭代上限**: 触发兜底处理机制
4. **用户中断**: 等待用户输入后继续

### 重试策略

- 最大重试次数由 `max_iterations` 参数控制
- 每次重试会增加 `iteration_count`
- 达到上限后自动进入兜底处理

## 扩展开发

### 添加新的子代理

1. 继承 `BaseAgent` 类
2. 实现 `build_nodes` 和 `build_edges` 方法
3. 定义适当的状态和上下文类型
4. 将代理添加到监督器的 `agents` 列表中

### 自定义任务路由

可以重写 `_determine_agent_for_task` 方法来实现自定义的任务路由逻辑：

```python
def _determine_agent_for_task(self, task_description: str) -> Optional[str]:
    # 自定义路由逻辑
    if "特定关键词" in task_description.lower():
        return "特定代理名称"
    return super()._determine_agent_for_task(task_description)
```

## 性能优化

### 并发执行

监督器支持任务的并发执行，通过 `_get_ready_tasks` 方法控制并发数量。

### 缓存机制

可以在子代理中实现缓存机制来避免重复计算。

### 资源管理

建议为长时间运行的监督器实例实现适当的资源清理机制。

## 调试和监控

### 日志记录

监督器使用标准的 Python logging 模块记录执行过程：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

### 状态检查

可以通过状态字段监控执行进度：

```python
# 检查当前执行状态
current_node = state.get("current_node")
completed_count = len(state.get("completed_tasks", []))
failed_count = len(state.get("failed_tasks", []))
```

## 已知限制

1. 当前版本的计划解析相对简单，复杂的 JSON 格式可能解析失败
2. 子代理之间的数据传递需要通过任务结果进行
3. 用户审核步骤目前需要手动处理中断

## 未来改进

- [ ] 更智能的计划解析和生成
- [ ] 支持更复杂的任务依赖关系
- [ ] 实现任务结果的直接传递
- [ ] 添加任务执行的可视化界面
- [ ] 支持分布式子代理部署
