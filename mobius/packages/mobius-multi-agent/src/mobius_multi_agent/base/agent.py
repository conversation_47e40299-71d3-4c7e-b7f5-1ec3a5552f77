import logging
from abc import ABC, abstractmethod
from typing import Any, Optional, Generic, Union, Sequence, Callable, get_args

from langchain_core.runnables.base import RunnableLike
from langchain_core.runnables.utils import Input as RInputT, Output as ROutputT
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient  # type: ignore
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import ToolNode
from langgraph.typing import StateT, ContextT, InputT, OutputT

logger = logging.getLogger(__name__)


class BaseAgent(ABC, Generic[StateT, ContextT, InputT, OutputT]):
    def __init__(
        self,
        name: str,
        description: str,
        *,
        tools: Optional[
            Union[
                Sequence[Union[BaseTool, Callable[..., Any], dict[str, Any]]], ToolNode
            ]
        ] = None,
        llm: Optional[ChatOpenAI] = None,
        mcp_client: Optional[MultiServerMCPClient] = None,
        max_iterations: int = 3,
        pre_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
        post_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
    ):
        self.name = name
        self.description = description
        self.llm = llm
        self.mcp_client = mcp_client
        self.max_iterations = max_iterations
        self._graph: Optional[CompiledStateGraph[StateT, ContextT, InputT, OutputT]] = (
            None
        )
        self.pre_model_hook = pre_model_hook
        self.post_model_hook = post_model_hook
        self.tools = tools

    @abstractmethod
    def build_nodes(
        self, builder: StateGraph[StateT, ContextT, InputT, OutputT]
    ) -> StateGraph[StateT, ContextT, InputT, OutputT]:
        """构建节点 - 子类实现具体业务逻辑"""
        pass

    @abstractmethod
    def build_edges(
        self, builder: StateGraph[StateT, ContextT, InputT, OutputT]
    ) -> StateGraph[StateT, ContextT, InputT, OutputT]:
        """构建边 - 子类实现路由逻辑"""
        pass

    def get_graph(self) -> CompiledStateGraph[StateT, ContextT, InputT, OutputT]:
        """模板方法 - 构建完整的执行图"""
        if self._graph is None:
            # 从泛型参数中提取具体类型
            agent_type = self.__class__.__orig_bases__[0]  # type: ignore
            type_args = get_args(agent_type)
            StateT_cls = type_args[0]
            ContextT_cls = type_args[1]
            InputT_cls = type_args[2]
            OutputT_cls = type_args[3]

            # 创建状态图
            builder: StateGraph[StateT, ContextT, InputT, OutputT] = StateGraph(
                state_schema=StateT_cls,
                context_schema=ContextT_cls,
                input_schema=InputT_cls,
                output_schema=OutputT_cls,
            )

            # 子类定义的节点
            builder = self.build_nodes(builder)

            # 子类定义的边
            builder = self.build_edges(builder)

            # 编译图
            self._graph = builder.compile()

        return self._graph

    def __repr__(self) -> str:
        return f"BaseAgent(name={self.name}, description={self.description})"
