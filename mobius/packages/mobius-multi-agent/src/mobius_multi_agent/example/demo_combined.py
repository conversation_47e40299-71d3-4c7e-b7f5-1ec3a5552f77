import asyncio
import logging
from pydantic import SecretStr, Field
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnableConfig

from mobius_multi_agent.example.unit_agent import UnitAgent
from mobius_multi_agent.example.math_agent import MathAgent
from mobius_multi_agent.supervisor.supervisor_agent import (
    SupervisorAgent,
    SupervisorInput,
)
from mobius_multi_agent.supervisor.models import SupervisorGraphState

# 配置日志记录
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def run_demo() -> None:
    """运行重构后的多代理系统演示"""
    # 1. 定义大语言模型 (LLM)
    llm = ChatOpenAI(
        api_key=SecretStr("2"),
        base_url="http://**************:16701/v1",
        model="qwen3_32b",
        temperature=0.0001,
        extra_body={"chat_template_kwargs": {"enable_thinking": False}},
    )

    # 2. 初始化子代理
    unit_agent = UnitAgent(
        name="UnitAgent",
        description="一个单位转换代理，可以处理公里到英里和摄氏度到华氏度的转换",
    )
    math_agent = MathAgent(
        name="MathAgent",
        description="一个数学表达式解析和评估代理",
    )

    # 3. 初始化监督器代理
    supervisor = SupervisorAgent(
        agents=[unit_agent, math_agent],
        llm=llm,
        name="Supervisor",
        description="一个多代理系统的监督器，负责规划和调度任务",
    )
    supervisor_graph = supervisor.get_graph()

    # 4. 定义复杂的用户请求
    user_query = "请将100公里转换为英里，然后将结果加上5，再乘以2，最后减去10。"
    logger.info(f"开始处理用户请求: {user_query}")

    # 5. 准备监督器初始状态和配置
    initial_supervisor_input = SupervisorInput(
        query=user_query,
        user_id="user-123",
        session_id="session-abc",
        config_overrides={"user_id": "user-123", "session_id": "session-abc"},
    )
    
    config: RunnableConfig = {
        "configurable": {
            "user_id": "user-123", 
            "session_id": "session-abc",
            "initial_input": initial_supervisor_input
        }
    }

    # 6. 执行工作流
    final_state = None
    # 传递一个空的初始状态，真正的输入在配置中
    async for step in supervisor_graph.astream({}, config=config):
        step_name = list(step.keys())[0]
        logger.info(f"--- 当前步骤: {step_name} ---")
        current_state = step[step_name]
        logger.info(f"阶段: {current_state.get('current_phase')}")
        if current_state.get("current_plan"):
            logger.info(f"计划: {current_state.get('current_plan')}")
        if current_state.get("agent_results"):
            logger.info(f"代理结果: {current_state.get('agent_results')}")
        final_state = step

    # 7. 打印最终结果
    if final_state:
        final_result_node = list(final_state.values())[0]
        logger.info("\n--- 工作流执行完毕 ---")

        last_math_result = None
        agent_results = final_result_node.get("agent_results", [])
        for result in reversed(agent_results):
            if result.get("agent_type") == "MathAgent" and result.get("success"):
                last_math_result = result.get("data")
                break

        logger.info(f"最终计算结果: {last_math_result}")
        logger.info(f"完整的最终状态: {final_result_node}")
    else:
        logger.error("工作流未能生成最终状态。")


if __name__ == "__main__":
    asyncio.run(run_demo())
