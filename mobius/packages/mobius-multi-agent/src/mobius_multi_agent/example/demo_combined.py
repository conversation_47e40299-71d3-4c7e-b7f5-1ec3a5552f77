"""
增强版 Supervisor Agent 系统演示

这个演示展示了如何使用增强版的多代理系统，包括：

基础功能：
- 多代理协调和任务分配
- 自动计划生成和执行
- 结果评估和汇总

增强功能（如果可用）：
- 依赖关系管理和DAG验证
- 异常处理和重试机制
- 检查点和状态管理
- 动态配置管理
- 提示词模板系统
- 评估量化体系

使用方法：
1. 基础运行: python demo_combined.py
2. 调试模式: python demo_combined.py --debug

注意：增强功能需要安装相应的依赖包，如果不可用会自动降级到基础功能。
"""

import asyncio
import logging
from pydantic import SecretStr
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnableConfig

from mobius_multi_agent.example.unit_agent import UnitAgent
from mobius_multi_agent.example.math_agent import MathAgent
from mobius_multi_agent.supervisor.supervisor_agent import (
    SupervisorAgent,
    SupervisorInput,
)
from mobius_multi_agent.supervisor.models import SupervisorGraphState

# 尝试导入增强功能模块
try:
    from mobius_multi_agent.supervisor.models import (
        ExecutionPlan, SubTask, TaskDependency, DependencyType,
        PerformanceMetrics, QualityMetric
    )
    ENHANCED_FEATURES = True
except ImportError:
    ENHANCED_FEATURES = False
    logging.warning("增强功能模块未找到，将使用基础功能")

# 配置日志记录
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def run_demo() -> None:
    """运行增强版多代理系统演示"""
    # 1. 定义大语言模型 (LLM)
    # llm = ChatOpenAI(
    #     api_key=SecretStr("2"),
    #     base_url="http://123.181.192.99:16701/v1",
    #     model="qwen3_32b",
    #     temperature=0.0001,
    #     extra_body={"chat_template_kwargs": {"enable_thinking": False}},
    # )
    llm = ChatOpenAI(
        base_url='https://api-inference.modelscope.cn/v1',
        api_key='ms-6ba02331-d56b-4058-a9ca-4b982fc52287',
        model="Qwen/Qwen3-30B-A3B-Thinking-2507",
        temperature=0.0001,
        max_tokens=4096,
        extra_body={"chat_template_kwargs":{"enable_thinking": False}}
    )

    # 2. 初始化子代理
    unit_agent = UnitAgent(
        name="UnitAgent",
        description="一个单位转换代理，可以处理公里到英里和摄氏度到华氏度的转换",
    )
    math_agent = MathAgent(
        name="MathAgent",
        description="一个数学表达式解析和评估代理",
    )

    # 3. 初始化增强版监督器代理
    supervisor = SupervisorAgent(
        agents=[unit_agent, math_agent],
        llm=llm,
        name="Supervisor",
        description="一个多代理系统的监督器，负责规划和调度任务",
        # 增强功能配置
        config_dir="./configs",
        prompts_dir="./prompts",
        checkpoints_dir="./checkpoints",
        max_iterations=3
    )

    # 显示系统状态
    if hasattr(supervisor, 'get_system_status'):
        system_status = await supervisor.get_system_status()
        logger.info(f"系统状态: {system_status}")

    supervisor_graph = supervisor.get_graph()

    # 4. 定义复杂的用户请求
    user_query = "请将100公里转换为英里，然后将结果加上5，再乘以2，最后减去10。"
    logger.info(f"开始处理用户请求: {user_query}")

    # 5. 准备监督器初始状态和配置
    initial_supervisor_input = SupervisorInput(
        query=user_query,
        user_id="user-123",
        session_id="session-abc",
        config_overrides={"user_id": "user-123", "session_id": "session-abc"},
    )

    config: RunnableConfig = {
        "configurable": {
            "user_id": "user-123",
            "session_id": "session-abc",
            "initial_input": initial_supervisor_input
        }
    }

    # 6. 执行工作流
    final_state = None
    try:
        # 使用正确的输入类型
        async for step in supervisor_graph.astream(initial_supervisor_input, config=config):
            step_name = list(step.keys())[0]
            logger.info(f"--- 当前步骤: {step_name} ---")
            current_state = step[step_name]

            # 显示当前阶段
            if isinstance(current_state, dict):
                logger.info(f"阶段: {current_state.get('current_phase')}")

                # 显示计划信息
                if current_state.get("current_plan"):
                    plan = current_state.get('current_plan')
                    if isinstance(plan, dict):
                        subtasks_count = len(plan.get('subtasks', []))
                        logger.info(f"执行计划: {plan.get('description', '未知')} (包含 {subtasks_count} 个任务)")
                    else:
                        logger.info(f"计划: {plan}")

                # 显示代理执行结果
                if current_state.get("agent_results"):
                    results = current_state.get('agent_results')
                    if results and isinstance(results, list):
                        logger.info(f"已完成 {len(results)} 个代理任务")
                        for i, result in enumerate(results[-3:]):  # 只显示最近3个结果
                            if isinstance(result, dict):
                                agent_type = result.get('agent_type', '未知')
                                success = result.get('success', False)
                                status = "成功" if success else "失败"
                                logger.info(f"  任务 {i+1}: {agent_type} - {status}")

                # 显示性能指标
                if current_state.get("performance_metrics") and ENHANCED_FEATURES:
                    metrics = current_state.get('performance_metrics')
                    if isinstance(metrics, dict):
                        success_rate = metrics.get('success_rate', 0)
                        quality_score = metrics.get('quality_score', 0)
                        logger.info(f"性能指标: 成功率={success_rate:.2f}, 质量分数={quality_score:.2f}")

            final_state = step

    except Exception as e:
        logger.error(f"工作流执行出错: {e}")

        # 如果有异常处理器，显示异常统计
        if hasattr(supervisor, 'exception_handler') and supervisor.exception_handler:
            stats = supervisor.exception_handler.get_exception_statistics()
            logger.info(f"异常统计: {stats}")

        raise

    # 7. 处理和显示最终结果
    if final_state:
        final_result_node = list(final_state.values())[0]
        logger.info("\n--- 工作流执行完毕 ---")

        # 提取最终计算结果
        last_math_result = None
        agent_results = final_result_node.get("agent_results", [])
        if isinstance(agent_results, list):
            for result in reversed(agent_results):
                if isinstance(result, dict) and result.get("agent_type") == "MathAgent" and result.get("success"):
                    last_math_result = result.get("data")
                    break

        logger.info(f"最终计算结果: {last_math_result}")

        # 显示增强功能的结果
        if ENHANCED_FEATURES and isinstance(final_result_node, dict):
            # 显示性能指标摘要
            if final_result_node.get("performance_metrics"):
                metrics = final_result_node.get("performance_metrics")
                if isinstance(metrics, dict):
                    logger.info("\n--- 性能指标摘要 ---")
                    logger.info(f"总任务数: {metrics.get('total_tasks', 0)}")
                    logger.info(f"完成任务数: {metrics.get('completed_tasks', 0)}")
                    logger.info(f"失败任务数: {metrics.get('failed_tasks', 0)}")
                    logger.info(f"成功率: {metrics.get('success_rate', 0):.2%}")
                    logger.info(f"质量分数: {metrics.get('quality_score', 0):.2f}")
                    logger.info(f"总执行时间: {metrics.get('total_execution_time', 0):.2f}秒")

            # 显示计划执行情况
            if final_result_node.get("current_plan"):
                plan = final_result_node.get("current_plan")
                if isinstance(plan, dict):
                    logger.info("\n--- 计划执行情况 ---")
                    logger.info(f"计划描述: {plan.get('description', '未知')}")
                    subtasks = plan.get('subtasks', [])
                    if isinstance(subtasks, list):
                        completed_count = sum(1 for task in subtasks if isinstance(task, dict) and task.get('status') == 'completed')
                        logger.info(f"任务完成情况: {completed_count}/{len(subtasks)}")

            # 显示对话历史
            conversation_history = final_result_node.get("conversation_history", [])
            if isinstance(conversation_history, list) and conversation_history:
                logger.info("\n--- 对话历史 ---")
                for entry in conversation_history[-3:]:  # 显示最后3条
                    if isinstance(entry, dict):
                        role = entry.get('role', '未知')
                        content = entry.get('content', '')
                        logger.info(f"{role}: {content[:100]}...")

        # 如果有检查点管理器，显示检查点信息
        if hasattr(supervisor, 'checkpoint_manager') and supervisor.checkpoint_manager:
            try:
                session_id = "session-abc"
                checkpoints = await supervisor.checkpoint_manager.list_checkpoints(session_id, limit=5)
                if checkpoints:
                    logger.info(f"\n--- 检查点信息 ---")
                    logger.info(f"会话 {session_id} 共有 {len(checkpoints)} 个检查点")
                    for cp in checkpoints[:3]:  # 显示最近3个
                        logger.info(f"  {cp.checkpoint_type}: {cp.description}")
            except Exception as e:
                logger.debug(f"获取检查点信息失败: {e}")

        # 显示完整状态（调试用）
        if logging.getLogger().isEnabledFor(logging.DEBUG):
            logger.debug(f"完整的最终状态: {final_result_node}")
    else:
        logger.error("工作流未能生成最终状态。")


async def run_enhanced_demo() -> None:
    """运行增强功能演示"""
    if not ENHANCED_FEATURES:
        logger.warning("增强功能不可用，跳过增强演示")
        return

    logger.info("\n=== 增强功能演示 ===")

    # 演示依赖关系管理
    logger.info("\n1. 依赖关系管理演示")
    try:
        # 导入增强功能类
        from mobius_multi_agent.supervisor.models import (
            ExecutionPlan, SubTask, TaskDependency, DependencyType,
            PerformanceMetrics, QualityMetric
        )

        # 创建带有依赖关系的执行计划
        plan = ExecutionPlan(
            description="复杂计算流水线",
            subtasks=[
                SubTask(
                    id="task_1",
                    description="单位转换：100公里转英里",
                    agent_type="UnitAgent",
                    agent_input={"task": "convert", "tool": "km_to_miles", "value": 100, "result": 0.0},
                    priority=1,
                    estimated_duration=10
                ),
                SubTask(
                    id="task_2",
                    description="数学计算：结果加5再乘2减10",
                    agent_type="MathAgent",
                    agent_input={"expression": "((x + 5) * 2) - 10", "user_input": "x"},
                    dependencies=[
                        TaskDependency(
                            task_id="task_1",
                            dependency_type=DependencyType.DATA
                        )
                    ],
                    priority=2,
                    estimated_duration=15
                )
            ]
        )

        # 验证DAG
        is_valid = plan.validate_dag()
        logger.info(f"计划DAG验证: {'通过' if is_valid else '失败'}")

        # 获取拓扑排序
        topo_order = plan.get_topological_order()
        logger.info(f"任务执行顺序: {topo_order}")

        # 获取就绪任务
        ready_tasks = plan.get_ready_tasks()
        logger.info(f"当前就绪任务: {[task.id for task in ready_tasks]}")

    except Exception as e:
        logger.error(f"依赖关系演示失败: {e}")

    # 演示性能指标
    logger.info("\n2. 性能指标演示")
    try:
        # 确保已导入PerformanceMetrics
        from mobius_multi_agent.supervisor.models import PerformanceMetrics

        metrics = PerformanceMetrics()
        metrics.total_tasks = 5
        metrics.completed_tasks = 4
        metrics.failed_tasks = 1
        metrics.total_execution_time = 45.5

        # 添加自定义质量指标
        metrics.add_quality_metric("accuracy", 0.95, 2.0, "结果准确性")
        metrics.add_quality_metric("efficiency", 0.88, 1.5, "执行效率")

        # 计算派生指标
        metrics.calculate_derived_metrics()

        # 获取质量摘要
        summary = metrics.get_quality_summary()
        logger.info(f"质量摘要: {summary}")

    except Exception as e:
        logger.error(f"性能指标演示失败: {e}")


async def main() -> None:
    """主函数：运行所有演示"""
    logger.info("=== 多代理系统演示开始 ===")

    # 运行基础演示
    logger.info("\n--- 基础功能演示 ---")
    await run_demo()

    # 运行增强功能演示
    logger.info("\n--- 增强功能演示 ---")
    await run_enhanced_demo()

    logger.info("\n=== 演示完成 ===")


if __name__ == "__main__":
    # 设置日志级别
    import sys
    if "--debug" in sys.argv:
        logging.getLogger().setLevel(logging.DEBUG)

    asyncio.run(main())
