"""
测试修复后的 ModelScope API 兼容性
"""

import asyncio
import logging
from pydantic import SecretStr
from langchain_openai import ChatOpenAI

from mobius_multi_agent.example.unit_agent import UnitAgent
from mobius_multi_agent.example.math_agent import MathAgent
from mobius_multi_agent.supervisor.supervisor_agent import (
    SupervisorAgent,
    SupervisorInput,
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_modelscope_compatibility():
    """测试 ModelScope API 兼容性"""
    
    # 使用 ModelScope API 配置
    llm = ChatOpenAI(
        base_url='https://api-inference.modelscope.cn/v1',
        api_key='ms-6ba02331-d56b-4058-a9ca-4b982fc52287',
        model="Qwen/Qwen3-30B-A3B-Thinking-2507",
        temperature=0.0001,
        max_tokens=4096,
        extra_body={"chat_template_kwargs":{"enable_thinking": False}}
    )
    
    # 创建代理
    unit_agent = UnitAgent(
        name="UnitAgent",
        description="单位转换代理",
    )
    math_agent = MathAgent(
        name="MathAgent", 
        description="数学计算代理",
    )
    
    # 创建监督器
    supervisor = SupervisorAgent(
        agents=[unit_agent, math_agent],
        llm=llm,
        name="TestSupervisor",
        description="测试监督器",
        max_iterations=2  # 减少迭代次数以便快速测试
    )
    
    # 简单的测试请求
    test_input = SupervisorInput(
        query="请将50公里转换为英里",
        user_id="test_user",
        session_id="test_session"
    )
    
    try:
        logger.info("开始测试...")
        graph = supervisor.get_graph()
        
        # 执行测试
        result = await graph.ainvoke(test_input)
        
        logger.info("测试成功完成!")
        logger.info(f"结果: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


async def test_fallback_plan():
    """测试兜底计划功能"""
    
    # 创建一个简单的监督器用于测试
    supervisor = SupervisorAgent(
        agents=[],  # 空代理列表
        llm=None,   # 无LLM
        name="FallbackTestSupervisor"
    )
    
    # 测试兜底计划创建
    test_requests = [
        "请将100公里转换为英里",
        "计算 2 + 3 * 4",
        "帮我处理一些数据",
        "这是一个通用请求"
    ]
    
    for request in test_requests:
        try:
            plan = supervisor._create_fallback_plan(request)
            logger.info(f"请求: {request}")
            logger.info(f"兜底计划: {plan.description}")
            logger.info(f"任务数量: {len(plan.subtasks)}")
            for task in plan.subtasks:
                logger.info(f"  - {task.description} ({task.agent_type})")
            logger.info("---")
        except Exception as e:
            logger.error(f"兜底计划创建失败: {e}")


async def main():
    """主测试函数"""
    logger.info("=== ModelScope API 兼容性测试 ===")
    
    # 测试兜底计划功能
    logger.info("\n1. 测试兜底计划功能")
    await test_fallback_plan()
    
    # 测试 ModelScope API
    logger.info("\n2. 测试 ModelScope API 兼容性")
    success = await test_modelscope_compatibility()
    
    if success:
        logger.info("\n✅ 所有测试通过!")
    else:
        logger.info("\n❌ 部分测试失败，但兜底机制应该能处理")


if __name__ == "__main__":
    asyncio.run(main())
