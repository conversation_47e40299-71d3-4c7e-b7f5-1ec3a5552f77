import json
from typing import Optional, Sequence, Union, Callable, Any, TypedDict, Dict

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables.base import RunnableLike
from langchain_core.runnables.utils import Input as RInputT, Output as ROutputT
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient  # type: ignore
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langgraph.runtime import Runtime
from langgraph.types import interrupt

from mobius_multi_agent.base import BaseAgent
from pydantic import SecretStr


def km_to_miles(km: float) -> float:
    """Convert kilometers to miles."""
    return km * 0.621371


def celsius_to_fahrenheit(celsius: float) -> float:
    """Convert Celsius to Fahrenheit."""
    return (celsius * 9 / 5) + 32


unit_tools = {
    "km_to_miles": km_to_miles,
    "celsius_to_fahrenheit": celsius_to_fahrenheit,
}


class UnitState(TypedDict):
    task: str
    tool: str
    value: float
    result: float


class UnitAgentContext(TypedDict):
    llm: ChatOpenAI


class UnitAgent(BaseAgent[UnitState, UnitAgentContext, UnitState, UnitState]):
    def __init__(
        self,
        name: str,
        description: str,
        *,
        tools: Optional[
            Union[
                Sequence[Union[BaseTool, Callable[..., Any], dict[str, Any]]], ToolNode
            ]
        ] = None,
        llm: Optional[ChatOpenAI] = None,
        mcp_client: Optional[MultiServerMCPClient] = None,
        max_iterations: int = 3,
        pre_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
        post_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
    ):
        super().__init__(
            name,
            description,
            tools=tools,
            llm=llm,
            mcp_client=mcp_client,
            max_iterations=max_iterations,
            pre_model_hook=pre_model_hook,
            post_model_hook=post_model_hook,
        )

    def unit_planner(
        self, state: UnitState, runtime: Runtime[UnitAgentContext]
    ) -> Dict[str, Any]:
        """解析单位转换任务并生成执行计划。"""
        system_prompt = """
Parse the unit conversion task (e.g., "将公里转换为英里" or "将摄氏度转换为华氏度").
Recognize Chinese: 公里=kilometers, 英里=miles, 摄氏度=Celsius, 华氏度=Fahrenheit.
If the task lacks a specific value (e.g., "将公里转换为英里"), use "user_input" as the value.
Output a JSON object: {"tool": str, "value": float or "user_input"}.
Supported tools: "km_to_miles", "celsius_to_fahrenheit".
Example: "将10公里转换为英里" -> {"tool": "km_to_miles", "value": 10}
Example: "将公里转换为英里" -> {"tool": "km_to_miles", "value": "user_input"}
Output only JSON object.
"""
        llm = runtime.context["llm"]
        response = llm.invoke(
            [SystemMessage(content=system_prompt), HumanMessage(content=state["task"])]
        )

        if isinstance(response.content, str):
            plan_str = response.content.strip()
            if plan_str.startswith("```json"):
                plan_str = plan_str[7:-3].strip()
            plan = json.loads(plan_str)
            return plan
        else:
            raise ValueError("LLM response content is not a string")

    def unit_executor(
        self, state: UnitState, runtime: Runtime[UnitAgentContext]
    ) -> Dict[str, Any]:
        """执行单位转换操作。"""
        value = state["value"]
        if value == "user_input":
            if state["tool"] == "km_to_miles":
                prompt = "Enter the number of kilometers: "
            elif state["tool"] == "celsius_to_fahrenheit":
                prompt = "Enter the temperature in Celsius: "
            else:
                prompt = "Enter the value: "
            value = interrupt(prompt)

        tool_func = unit_tools.get(state["tool"])
        if not tool_func:
            raise ValueError(f"Unknown tool: {state['tool']}")

        result = tool_func(value)
        return {"result": result}

    def build_nodes(
        self, builder: StateGraph[UnitState, UnitAgentContext, UnitState, UnitState]
    ) -> StateGraph[UnitState, UnitAgentContext, UnitState, UnitState]:
        """构建节点 - 添加规划器和执行器节点。"""
        builder.add_node("planner", self.unit_planner)
        builder.add_node("executor", self.unit_executor)
        return builder

    def build_edges(
        self, builder: StateGraph[UnitState, UnitAgentContext, UnitState, UnitState]
    ) -> StateGraph[UnitState, UnitAgentContext, UnitState, UnitState]:
        """构建边 - 定义节点之间的路由逻辑。"""
        builder.set_entry_point("planner")
        builder.add_edge("planner", "executor")
        builder.add_edge("executor", END)
        return builder


def build_unit_agent() -> UnitAgent:
    """构建单位转换代理的工厂函数。"""
    return UnitAgent(
        name="Unit Conversion Agent",
        description="A unit conversion agent that handles kilometers to miles and Celsius to Fahrenheit conversions",
    )


if __name__ == "__main__":

    # Define the LLM
    llm = ChatOpenAI(
        api_key=SecretStr("2"),
        base_url="http://**************:16701/v1",
        model="qwen3_32b",
        temperature=0.0001,
        extra_body={"chat_template_kwargs": {"enable_thinking": False}},
    )
    agent = UnitAgent(
        name="Unit Conversion Agent",
        description="A unit conversion agent that handles kilometers to miles and Celsius to Fahrenheit conversions",
        # llm=llm,
    )
    graph = agent.get_graph()
    unit_state: UnitState = {
        "task": "将10公里转换为英里",
        "tool": "",
        "value": 0.0,
        "result": 0.0,
    }
    result = graph.invoke(unit_state, context=UnitAgentContext({"llm": llm}))
    print(result)
