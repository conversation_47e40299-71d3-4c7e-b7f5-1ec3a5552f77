import json
from typing import Optional, List, Sequence, Union, Callable, Any, TypedDict, Dict
from unittest.mock import MagicMixin

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables.base import Runnable<PERSON>ike
from langchain_core.runnables.utils import Input as RInputT, Output as ROutputT
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient  # type: ignore
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langgraph.runtime import Runtime
from langgraph.types import interrupt

from mobius_multi_agent.base import BaseAgent
from pydantic import SecretStr


def add(a: float, b: float) -> float:
    return a + b


def subtract(a: float, b: float) -> float:
    return a - b


def multiply(a: float, b: float) -> float:
    return a * b


def divide(a: float, b: float) -> float:
    if b == 0:
        raise ValueError("Division by zero")
    return a / b


def power(a: float, b: float) -> float:
    return a**b


math_tools = {
    "add": add,
    "subtract": subtract,
    "multiply": multiply,
    "divide": divide,
    "power": power,
}


class MathState(TypedDict):
    expression: str
    plan: List[Dict[str, Any]]
    user_input: float
    result: float


class MathAgentContext(TypedDict):
    llm: ChatOpenAI


class MathSubgraphState(TypedDict):
    results: Dict[str, float]


class MathAgent(BaseAgent[MathState, MathAgentContext, MathState, MathState]):
    def __init__(
        self,
        name: str,
        description: str,
        *,
        tools: Optional[
            Union[
                Sequence[Union[BaseTool, Callable[..., Any], dict[str, Any]]], ToolNode
            ]
        ] = None,
        llm: Optional[ChatOpenAI] = None,
        mcp_client: Optional[MultiServerMCPClient] = None,
        max_iterations: int = 3,
        pre_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
        post_model_hook: Optional[RunnableLike[RInputT, ROutputT]] = None,
    ):
        super().__init__(
            name,
            description,
            tools=tools,
            llm=llm,
            mcp_client=mcp_client,
            max_iterations=max_iterations,
            pre_model_hook=pre_model_hook,
            post_model_hook=post_model_hook,
        )

    def math_planner(
        self, state: MathState, runtime: Runtime[MathAgentContext]
    ) -> dict[str, dict[str, Any]]:
        system_prompt = """
            You are a mathematical expression parser handling Chinese natural language. Parse the expression into atomic operations, respecting PEMDAS/BODMAS.
            Convert Chinese: 一=1, 二=2, 三=3, 四=4, 五=5, etc.; 加=add, 减=subtract, 乘以=multiply, 除以=divide, 的值次方=power.
            "我输入的值" or similar means "user_input" tool with empty args.
            Output JSON list of steps: {"id": str, "tool": str, "args": List[Union[float, str]]}.
            Example: "二加三的值乘以四减一的值" -> [{"id": "1", "tool": "add", "args": [2, 3]}, {"id": "2", "tool": "subtract", "args": [4, 1]}, {"id": "3", "tool": "multiply", "args": ["1", "2"]}]
            Output only JSON list.
            """
        llm = runtime.context["llm"]
        response = llm.invoke(
            [
                SystemMessage(content=system_prompt),
                HumanMessage(content=state["expression"]),
            ]
        )
        if isinstance(response.content, str):
            plan_str = response.content

            plan_str = plan_str.strip()
            if plan_str.startswith("```json"):
                plan_str = plan_str[7:-3].strip()
            plan = json.loads(plan_str)
            return {"plan": plan}
        return {"plan": {}}

    def math_executor(self, state: MathState) -> dict[str, dict[str, Any]]:
        plan = state["plan"]
        subgraph = StateGraph(MathSubgraphState)
        previous_node = None
        first_node: str = ""
        last_id: str = ""
        for step in plan:
            step_id = step["id"]
            last_id = step_id

            def create_execute_step(
                step: dict[str, Any], main_state: MathState
            ) -> Callable[[MathSubgraphState], dict[str, dict[str, Any]]]:
                def execute_step(
                    sub_state: MathSubgraphState,
                ) -> dict[str, dict[str, Any]]:
                    if step["tool"] == "user_input":
                        if main_state.get("user_input") is None:
                            value = interrupt("Enter the value for '我输入的值':")
                            main_state["user_input"] = value
                        result = main_state["user_input"]
                    else:
                        resolved_args = []
                        for arg in step["args"]:
                            if isinstance(arg, (int, float)):
                                resolved_args.append(float(arg))
                            elif isinstance(arg, str) and arg == "user_input":
                                resolved_args.append(main_state["user_input"])
                            elif isinstance(arg, str):
                                resolved_args.append(sub_state["results"][arg])
                            else:
                                raise ValueError(f"Invalid arg: {arg}")
                        tool_func = math_tools.get(step["tool"])
                        if tool_func is None:
                            raise ValueError(f"Unknown tool: {step['tool']}")
                        result = tool_func(*resolved_args)
                    sub_state["results"][step["id"]] = result
                    return {"results": sub_state["results"]}

                return execute_step

            subgraph.add_node(step_id, create_execute_step(step, state))  # type: ignore
            if previous_node:
                subgraph.add_edge(previous_node, step_id)
            if not first_node:
                first_node = step_id
            previous_node = step_id
        subgraph.set_entry_point(first_node)
        subgraph.add_edge(last_id, END)
        app = subgraph.compile(checkpointer=InMemorySaver())
        final_sub_state = app.invoke(MathSubgraphState({"results": {}}))  # type: ignore
        return {"result": final_sub_state["results"].get(last_id)}

    def build_nodes(
        self, builder: StateGraph[MathState, MathAgentContext, MathState, MathState]
    ) -> StateGraph[MathState, MathAgentContext, MathState, MathState]:
        builder.add_node("planner", self.math_planner)
        builder.add_node("executor", self.math_executor)
        return builder

    def build_edges(
        self, builder: StateGraph[MathState, MathAgentContext, MathState, MathState]
    ) -> StateGraph[MathState, MathAgentContext, MathState, MathState]:
        builder.set_entry_point("planner")
        builder.add_edge("planner", "executor")
        builder.add_edge("executor", END)
        return builder


if __name__ == "__main__":
    # Define the LLM
    llm = ChatOpenAI(
        api_key=SecretStr("2"),
        base_url="http://**************:16701/v1",
        model="qwen3_32b",
        temperature=0.0001,
        extra_body={"chat_template_kwargs": {"enable_thinking": False}},
    )
    math_agent = MathAgent(
        name="Math Agent",
        description="A mathematical expression parser and evaluator",
    )
    result = math_agent.get_graph().stream(
        MathState(
            {"expression": "一加二乘三减四", "plan": [], "user_input": 0, "result": 0}
        ),
        context=MathAgentContext({"llm": llm}),
    )
    for step in result:
        print(step)
