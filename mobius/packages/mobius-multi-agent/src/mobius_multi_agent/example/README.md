# 增强版 Supervisor Agent 演示

## 概述

`demo_combined.py` 已经适配为增强版 Supervisor Agent 系统，展示了基础功能和增强功能的使用。

## 功能特性

### 基础功能
- ✅ 多代理协调和任务分配
- ✅ 自动计划生成和执行
- ✅ 结果评估和汇总
- ✅ 实时执行状态监控

### 增强功能（如果可用）
- 🔗 依赖关系管理和DAG验证
- 🛡️ 异常处理和重试机制
- 💾 检查点和状态管理
- ⚙️ 动态配置管理
- 📝 提示词模板系统
- 📊 评估量化体系

## 使用方法

### 1. 基础运行
```bash
cd mobius/packages/mobius-multi-agent/src/mobius_multi_agent/example
python demo_combined.py
```

### 2. 调试模式
```bash
python demo_combined.py --debug
```

### 3. 自定义配置
演示会自动创建以下目录结构（如果增强功能可用）：
```
./configs/          # 配置文件
./prompts/          # 提示词模板
./checkpoints/      # 检查点存储
```

## 演示内容

### 基础演示
1. **任务定义**: "请将100公里转换为英里，然后将结果加上5，再乘以2，最后减去10。"
2. **代理协调**: 自动分配给 UnitAgent 和 MathAgent
3. **执行监控**: 实时显示执行状态和结果
4. **结果汇总**: 展示最终计算结果和执行统计

### 增强功能演示
1. **依赖关系管理**: 
   - 创建带有数据依赖的任务链
   - DAG验证和拓扑排序
   - 就绪任务识别

2. **性能指标**: 
   - 自定义质量指标
   - 实时性能计算
   - 质量摘要生成

## 输出示例

### 基础功能输出
```
INFO - 系统状态: {'agents': {'total': 2, 'available': ['UnitAgent', 'MathAgent']}, ...}
INFO - 开始处理用户请求: 请将100公里转换为英里，然后将结果加上5，再乘以2，最后减去10。
INFO - --- 当前步骤: plan_generation ---
INFO - 阶段: executing
INFO - 执行计划: 复杂计算任务 (包含 2 个任务)
INFO - --- 当前步骤: task_execution ---
INFO - 已完成 1 个代理任务
INFO -   任务 1: UnitAgent - 成功
INFO - 已完成 2 个代理任务
INFO -   任务 1: UnitAgent - 成功
INFO -   任务 2: MathAgent - 成功
INFO - 最终计算结果: {'result': 115.1}
```

### 增强功能输出
```
INFO - --- 性能指标摘要 ---
INFO - 总任务数: 2
INFO - 完成任务数: 2
INFO - 失败任务数: 0
INFO - 成功率: 100.00%
INFO - 质量分数: 0.95
INFO - 总执行时间: 1.23秒

INFO - --- 计划执行情况 ---
INFO - 计划描述: 复杂计算任务
INFO - 任务完成情况: 2/2

INFO - --- 检查点信息 ---
INFO - 会话 session-abc 共有 3 个检查点
INFO -   plan_generated: 计划生成完成，包含 2 个任务
INFO -   task_completed: 任务执行完成
INFO -   execution_completed: 工作流执行完毕
```

## 配置说明

### LLM 配置
演示使用的是本地部署的 Qwen 模型，你可以修改以下配置：

```python
llm = ChatOpenAI(
    api_key=SecretStr("your-api-key"),
    base_url="your-api-endpoint",
    model="your-model-name",
    temperature=0.0001,
)
```

### 增强功能配置
如果要启用增强功能，确保以下模块可用：
- `prompt_manager.py` - 提示词模板管理
- `exception_handler.py` - 异常处理
- `checkpoint_manager.py` - 检查点管理
- `config_manager.py` - 配置管理

## 故障排除

### 常见问题

1. **增强功能不可用**
   ```
   WARNING - 增强功能模块未找到，将使用基础功能
   ```
   - 原因：增强功能模块未正确安装或导入失败
   - 解决：检查模块文件是否存在，确保没有语法错误

2. **LLM 连接失败**
   ```
   ERROR - 计划生成失败: LLM 未配置
   ```
   - 原因：LLM 配置错误或网络连接问题
   - 解决：检查 API 密钥、端点URL和网络连接

3. **任务执行超时**
   ```
   ERROR - 任务执行失败: timeout
   ```
   - 原因：任务执行时间过长
   - 解决：增加超时设置或优化任务逻辑

### 调试技巧

1. **启用详细日志**
   ```bash
   python demo_combined.py --debug
   ```

2. **检查系统状态**
   演示会自动显示系统状态，包括可用代理和管理器状态

3. **查看检查点**
   如果启用了检查点功能，可以查看 `./checkpoints/` 目录下的文件

## 扩展开发

### 添加新代理
1. 继承 `BaseAgent` 类
2. 实现 `build_nodes` 和 `build_edges` 方法
3. 在演示中添加到代理列表

### 自定义任务
1. 修改 `user_query` 变量
2. 根据需要调整代理配置
3. 添加特定的评估指标

### 配置增强功能
1. 创建自定义提示词模板
2. 配置异常处理策略
3. 设置检查点保存策略

## 相关文档

- [系统设计文档](../docs/enhanced_supervisor_system.md)
- [API 参考](../supervisor/)
- [基础代理示例](./math_agent.py, ./unit_agent.py)

## 更新日志

### v2.0.0 (当前版本)
- ✅ 适配增强版 Supervisor Agent 系统
- ✅ 添加增强功能演示
- ✅ 改进错误处理和日志记录
- ✅ 支持调试模式和详细输出

### v1.0.0 (原版本)
- ✅ 基础多代理协调演示
- ✅ 简单的任务执行和结果显示
