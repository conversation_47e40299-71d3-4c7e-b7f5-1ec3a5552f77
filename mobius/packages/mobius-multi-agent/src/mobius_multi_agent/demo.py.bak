from langchain_openai import ChatOpenAI

from langgraph_supervisor import create_supervisor
from langgraph.prebuilt import create_react_agent

model = ChatOpenAI(
    base_url="https://api-inference.modelscope.cn/v1",
    api_key="ms-6ba02331-d56b-4058-a9ca-4b982fc52287",
    model="Qwen/Qwen3-30B-A3B-Thinking-2507",
    temperature=0.0001,
    max_tokens=4096,
    extra_body={"chat_template_kwargs": {"enable_thinking": False}},
)


# Create specialized agents


def add(a: float, b: float) -> float:
    """Add two numbers."""
    return a + b


def multiply(a: float, b: float) -> float:
    """Multiply two numbers."""
    return a * b


def web_search(query: str) -> str:
    """Search the web for information."""
    return """以下是2024年每家FAANG公司的员工人数：
            1. **Facebook（Meta）**：67,317名员工。
            2. **Apple（苹果）**：164,000名员工。
            3. **Amazon（亚马逊）**：1,551,000名员工。
            4. **Netflix（奈飞）**：14,000名员工。
            5. **Google（Alphabet）**：181,269名员工。
        """


math_agent = create_react_agent(
    model=model,
    tools=[add, multiply],
    name="math_expert",
    prompt="You are a math expert. Always use one tool at a time.",
)

research_agent = create_react_agent(
    model=model,
    tools=[web_search],
    name="research_expert",
    prompt="You are a world class researcher with access to web search. Do not do any math.",
)

# Create supervisor workflow
workflow = create_supervisor(
    [research_agent, math_agent],
    model=model,
    prompt=(
        "You are a team supervisor managing a research expert and a math expert. "
        "For current events, use research_agent. "
        "For math problems, use math_agent."
    ),
)

# Compile and run
app = workflow.compile()
msg_types = set()
for agent, msg_type, data in app.stream(
    {"messages": [{"role": "user", "content": "2024年FAANG公司的员工总数是多少？"}]},
    stream_mode=["messages", "updates"],
    subgraphs=True,
):
    if agent:
        # print("agent:",agent)
        pass
    if msg_type == "updates" and data.get("__interrupt__"):
        has_interrupt = True
        interrupt_msg = data.get("__interrupt__")[0].value
    if msg_type == "messages":
        print(data[0].content, end="", flush=True)
    msg_types.add(msg_type)
    print(msg_types)
