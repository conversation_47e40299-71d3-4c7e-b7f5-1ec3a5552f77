# 增强版 Supervisor Agent 系统设计文档

## 概述

基于 LangGraph 和 Pydantic 的通用 Supervisor Agent 系统，提供完整的多代理协调、任务管理、异常处理和状态管理功能。

## 系统架构

### 核心组件

1. **Supervisor Agent** - 核心监督器
2. **Plan Manager** - 计划管理器
3. **Task Executor** - 任务执行器
4. **Result Evaluator** - 结果评估器
5. **Config Manager** - 配置管理器
6. **Exception Handler** - 异常处理器
7. **Checkpoint Manager** - 检查点管理器
8. **Prompt Manager** - 提示词管理器

### 工作流程

```
用户输入 → 计划生成 → 用户审核(可选) → 任务执行 → 结果评估 → 计划修订(如需) → 循环控制
```

## 核心功能

### 1. 依赖关系标准化

#### 依赖类型
- **Sequential**: 顺序依赖，任务B必须在任务A完成后执行
- **Conditional**: 条件依赖，基于任务A的结果决定是否执行任务B
- **Resource**: 资源依赖，任务B需要任务A释放的资源
- **Data**: 数据依赖，任务B需要任务A的输出数据

#### 依赖定义示例
```python
from mobius_multi_agent.supervisor.models import TaskDependency, DependencyType, DependencyCondition

dependency = TaskDependency(
    task_id="task_1",
    dependency_type=DependencyType.CONDITIONAL,
    condition=DependencyCondition(
        condition_type="result_match",
        expected_value="success"
    ),
    timeout_seconds=300,
    required=True
)
```

#### DAG 验证
系统自动验证任务依赖关系是否构成有效的有向无环图(DAG)，防止循环依赖。

### 2. 评估量化体系

#### 性能指标
- **成功率** (Success Rate): 完成任务数 / 总任务数
- **完成率** (Completion Rate): (完成+失败任务数) / 总任务数
- **效率分数** (Efficiency Score): 基于时间和成功率的综合评分
- **质量分数** (Quality Score): 基于多维度质量指标的综合评分

#### 质量指标
```python
from mobius_multi_agent.supervisor.models import QualityMetric

# 添加自定义质量指标
state.add_quality_metric(
    name="accuracy",
    value=0.95,
    weight=2.0,
    description="结果准确性",
    threshold=0.9
)
```

### 3. 提示词模板系统

#### 模板类型
- **PLAN_GENERATION**: 计划生成
- **TASK_EXECUTION**: 任务执行
- **RESULT_EVALUATION**: 结果评估
- **USER_INTERACTION**: 用户交互
- **ERROR_HANDLING**: 错误处理

#### 使用示例
```python
from mobius_multi_agent.supervisor.prompt_manager import PromptManager

prompt_manager = PromptManager("./prompts")

# 渲染模板
content = prompt_manager.render_template(
    "plan_generation_advanced",
    {
        "user_query": "用户查询",
        "available_agents": ["Agent1", "Agent2"],
        "context_info": "上下文信息"
    },
    scenario="complex_task"
)
```

#### 场景配置
支持不同场景下的模板映射和默认上下文：
- **simple_task**: 简单任务场景
- **complex_task**: 复杂任务场景
- **data_analysis**: 数据分析场景
- **error_recovery**: 错误恢复场景

### 4. 异常处理和重试机制

#### 异常类型
- **AGENT_ERROR**: 代理执行错误
- **TIMEOUT_ERROR**: 超时错误
- **DEPENDENCY_ERROR**: 依赖错误
- **VALIDATION_ERROR**: 验证错误
- **NETWORK_ERROR**: 网络错误

#### 重试策略
- **IMMEDIATE**: 立即重试
- **LINEAR_BACKOFF**: 线性退避
- **EXPONENTIAL_BACKOFF**: 指数退避
- **FIXED_DELAY**: 固定延迟
- **NO_RETRY**: 不重试

#### 兜底策略
- **SKIP_TASK**: 跳过任务
- **SIMPLIFIED_APPROACH**: 简化方法
- **ALTERNATIVE_AGENT**: 替代代理
- **MANUAL_INTERVENTION**: 人工干预
- **ABORT_PLAN**: 中止计划

### 5. 检查点和状态管理

#### 检查点类型
- **PLAN_GENERATED**: 计划生成完成
- **TASK_STARTED**: 任务开始执行
- **TASK_COMPLETED**: 任务完成
- **TASK_FAILED**: 任务失败
- **USER_INTERACTION**: 用户交互
- **PLAN_REVISED**: 计划修订

#### 使用示例
```python
from mobius_multi_agent.supervisor.checkpoint_manager import CheckpointManager

checkpoint_manager = CheckpointManager("./checkpoints")

# 创建检查点
checkpoint_id = await checkpoint_manager.create_checkpoint(
    session_id="session_001",
    state=current_state,
    checkpoint_type=CheckpointType.TASK_COMPLETED,
    description="任务执行完成"
)

# 恢复检查点
restored_state = await checkpoint_manager.restore_checkpoint(checkpoint_id)
```

### 6. 动态配置管理

#### 配置类型
- **Agent**: 代理配置
- **Tool**: 工具配置
- **LLM**: 语言模型配置
- **MCP Server**: MCP服务器配置

#### 代理注册示例
```python
from mobius_multi_agent.supervisor.config_manager import ConfigManager, AgentConfig

config_manager = ConfigManager("./configs")

agent_config = AgentConfig(
    name="CustomAgent",
    class_path="path.to.CustomAgent",
    description="自定义代理",
    capabilities=["能力1", "能力2"],
    input_schema={"param1": "type1", "param2": "type2"}
)

await config_manager.register_agent(agent_config)
```

## 使用指南

### 1. 基本使用

```python
from mobius_multi_agent.supervisor.supervisor_agent import SupervisorAgent, SupervisorInput
from langchain_openai import ChatOpenAI

# 创建 LLM
llm = ChatOpenAI(model="gpt-4", api_key="your-api-key")

# 创建子代理
agents = [MathAgent(llm=llm), UnitAgent(llm=llm)]

# 创建 Supervisor
supervisor = SupervisorAgent(
    agents=agents,
    llm=llm,
    config_dir="./configs",
    prompts_dir="./prompts",
    checkpoints_dir="./checkpoints"
)

# 执行任务
input_data = SupervisorInput(
    query="请计算 (10 + 20) * 2 并转换为英尺",
    user_id="user_001",
    session_id="session_001"
)

graph = supervisor.get_graph()
result = await graph.ainvoke(input_data)
```

### 2. 高级配置

#### 自定义提示词模板
在 `prompts/` 目录下创建 `.jinja2` 文件：

```jinja2
<!-- plan_generation_custom.jinja2 -->
作为智能助手，请分析以下请求：

用户查询: {{ user_query }}
可用资源: {{ available_agents | join(', ') }}

请生成详细的执行计划...
```

#### 场景配置
在 `prompts/scenarios.json` 中定义场景：

```json
{
  "name": "custom_scenario",
  "description": "自定义场景",
  "template_mappings": {
    "plan_generation": "plan_generation_custom"
  },
  "default_context": {
    "max_tasks": 5,
    "require_approval": true
  }
}
```

### 3. 监控和调试

#### 获取系统状态
```python
status = await supervisor.get_system_status()
print(f"系统状态: {status}")
```

#### 异常统计
```python
if supervisor.exception_handler:
    stats = supervisor.exception_handler.get_exception_statistics()
    print(f"异常统计: {stats}")
```

#### 检查点管理
```python
if supervisor.checkpoint_manager:
    checkpoints = await supervisor.checkpoint_manager.list_checkpoints("session_001")
    print(f"检查点数量: {len(checkpoints)}")
```

## 最佳实践

### 1. 任务设计
- 将复杂任务分解为独立的子任务
- 明确定义任务间的依赖关系
- 设置合理的超时时间和重试策略

### 2. 错误处理
- 为不同类型的错误配置适当的重试策略
- 实现兜底机制确保系统稳定性
- 记录详细的错误信息便于调试

### 3. 性能优化
- 合理设置并发执行的任务数量
- 使用检查点机制避免重复计算
- 定期清理旧的检查点和日志

### 4. 安全考虑
- 保护敏感配置信息（如API密钥）
- 验证用户输入防止注入攻击
- 限制系统资源使用防止滥用

## 扩展开发

### 1. 自定义代理
继承 `BaseAgent` 类实现自定义代理：

```python
from mobius_multi_agent.base.agent import BaseAgent

class CustomAgent(BaseAgent):
    def build_nodes(self, builder):
        # 实现节点构建逻辑
        pass
    
    def build_edges(self, builder):
        # 实现边构建逻辑
        pass
```

### 2. 自定义工具
实现 LangChain 工具接口：

```python
from langchain_core.tools import BaseTool

class CustomTool(BaseTool):
    name = "custom_tool"
    description = "自定义工具描述"
    
    def _run(self, query: str) -> str:
        # 实现工具逻辑
        return "工具执行结果"
```

### 3. 自定义评估指标
扩展性能指标系统：

```python
def custom_quality_evaluator(state: SupervisorGraphState) -> float:
    # 实现自定义质量评估逻辑
    return quality_score

# 注册自定义评估器
state.add_quality_metric("custom_metric", custom_quality_evaluator(state))
```

## 故障排除

### 常见问题

1. **导入错误**: 确保所有依赖包已正确安装
2. **配置文件错误**: 检查JSON格式和必需字段
3. **检查点恢复失败**: 验证检查点文件完整性
4. **任务执行超时**: 调整超时设置或优化任务逻辑

### 调试技巧

1. 启用详细日志记录
2. 使用检查点功能追踪状态变化
3. 监控异常统计识别问题模式
4. 使用系统状态API检查组件健康状态

## 更新日志

### v2.0.0 (当前版本)
- 新增依赖关系标准化支持
- 实现完整的异常处理和重试机制
- 添加检查点和状态管理功能
- 支持动态配置管理
- 引入提示词模板系统
- 建立评估量化体系

### v1.0.0 (基础版本)
- 基本的多代理协调功能
- 简单的任务执行和结果评估
- 基础的状态管理
