"""
增强版 Supervisor Agent 系统演示

展示新功能：
- 依赖关系管理
- 异常处理和重试
- 检查点和状态管理
- 动态配置管理
- 提示词模板系统
- 评估量化体系
"""

import asyncio
import logging
from typing import Dict, Any

from langchain_openai import ChatOpenAI

from mobius_multi_agent.supervisor.supervisor_agent import SupervisorAgent, SupervisorInput
from mobius_multi_agent.example.math_agent import MathAgent
from mobius_multi_agent.example.unit_agent import UnitAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def demo_enhanced_supervisor():
    """演示增强版 Supervisor Agent"""
    
    # 初始化 LLM
    llm = ChatOpenAI(
        model="gpt-4",
        temperature=0.1,
        api_key="your-api-key-here"  # 请替换为实际的API密钥
    )
    
    # 创建子代理
    math_agent = MathAgent(llm=llm)
    unit_agent = UnitAgent(llm=llm)
    
    # 创建增强版 Supervisor Agent
    supervisor = SupervisorAgent(
        agents=[math_agent, unit_agent],
        llm=llm,
        max_iterations=5,
        config_dir="./configs",
        prompts_dir="./prompts", 
        checkpoints_dir="./checkpoints"
    )
    
    # 获取系统状态
    system_status = await supervisor.get_system_status()
    logger.info(f"系统状态: {system_status}")
    
    # 创建复杂任务输入
    complex_input = SupervisorInput(
        query="请帮我计算 (15 + 25) * 2 的结果，然后将结果从米转换为英尺，最后生成一个包含计算过程的报告",
        user_id="demo_user",
        session_id="demo_session_001"
    )
    
    try:
        # 获取 Supervisor 图
        supervisor_graph = supervisor.get_graph()
        
        # 执行任务
        logger.info("开始执行复杂任务...")
        final_state = await supervisor_graph.ainvoke(
            complex_input,
            config={
                "configurable": {
                    "thread_id": "demo_session_001",
                    "initial_input": complex_input
                }
            }
        )
        
        logger.info("任务执行完成!")
        logger.info(f"最终状态: {final_state}")
        
        # 获取评估摘要
        if hasattr(final_state, 'get_evaluation_summary'):
            evaluation = final_state.get_evaluation_summary()
            logger.info(f"评估摘要: {evaluation}")
        
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        
        # 如果有异常处理器，获取异常统计
        if supervisor.exception_handler:
            stats = supervisor.exception_handler.get_exception_statistics()
            logger.info(f"异常统计: {stats}")


async def demo_dependency_management():
    """演示依赖关系管理"""
    
    from mobius_multi_agent.supervisor.models import (
        ExecutionPlan, SubTask, TaskDependency, DependencyType, DependencyCondition
    )
    
    # 创建带有复杂依赖关系的执行计划
    plan = ExecutionPlan(
        description="数据处理和分析流水线",
        subtasks=[
            SubTask(
                id="task_1",
                description="收集原始数据",
                agent_type="DataCollectorAgent",
                agent_input={"source": "database", "query": "SELECT * FROM sales"},
                priority=1,
                estimated_duration=30
            ),
            SubTask(
                id="task_2", 
                description="验证数据质量",
                agent_type="DataValidatorAgent",
                agent_input={"validation_rules": ["not_null", "range_check"]},
                dependencies=[
                    TaskDependency(
                        task_id="task_1",
                        dependency_type=DependencyType.DATA,
                        condition=DependencyCondition(
                            condition_type="success"
                        )
                    )
                ],
                priority=2,
                estimated_duration=20
            ),
            SubTask(
                id="task_3",
                description="数据分析",
                agent_type="AnalysisAgent", 
                agent_input={"analysis_type": "statistical"},
                dependencies=[
                    TaskDependency(
                        task_id="task_2",
                        dependency_type=DependencyType.CONDITIONAL,
                        condition=DependencyCondition(
                            condition_type="result_match",
                            expected_value="valid"
                        )
                    )
                ],
                priority=3,
                estimated_duration=60
            ),
            SubTask(
                id="task_4",
                description="生成报告",
                agent_type="ReportAgent",
                agent_input={"format": "pdf", "template": "standard"},
                dependencies=[
                    TaskDependency(
                        task_id="task_3",
                        dependency_type=DependencyType.DATA
                    )
                ],
                priority=4,
                estimated_duration=15
            )
        ]
    )
    
    # 验证 DAG
    is_valid_dag = plan.validate_dag()
    logger.info(f"计划是否为有效DAG: {is_valid_dag}")
    
    # 获取拓扑排序
    topo_order = plan.get_topological_order()
    logger.info(f"拓扑排序: {topo_order}")
    
    # 获取就绪任务
    ready_tasks = plan.get_ready_tasks()
    logger.info(f"就绪任务: {[task.id for task in ready_tasks]}")


async def demo_checkpoint_management():
    """演示检查点管理"""
    
    try:
        from mobius_multi_agent.supervisor.checkpoint_manager import CheckpointManager, CheckpointType
        from mobius_multi_agent.supervisor.models import SupervisorGraphState, WorkflowPhase
        
        # 创建检查点管理器
        checkpoint_manager = CheckpointManager("./demo_checkpoints")
        
        # 创建示例状态
        state = SupervisorGraphState(
            original_request="演示检查点功能",
            current_phase=WorkflowPhase.PLANNING,
            available_agents=["MathAgent", "UnitAgent"]
        )
        
        # 创建检查点
        checkpoint_id = await checkpoint_manager.create_checkpoint(
            session_id="demo_session",
            state=state,
            checkpoint_type=CheckpointType.PLAN_GENERATED,
            description="演示检查点创建"
        )
        
        logger.info(f"创建检查点: {checkpoint_id}")
        
        # 列出检查点
        checkpoints = await checkpoint_manager.list_checkpoints("demo_session")
        logger.info(f"会话检查点: {len(checkpoints)} 个")
        
        # 获取检查点统计
        stats = await checkpoint_manager.get_checkpoint_statistics("demo_session")
        logger.info(f"检查点统计: {stats}")
        
    except ImportError:
        logger.warning("检查点管理器模块未找到，跳过演示")


async def demo_config_management():
    """演示动态配置管理"""
    
    try:
        from mobius_multi_agent.supervisor.config_manager import (
            ConfigManager, AgentConfig, ToolConfig, LLMConfig
        )
        
        # 创建配置管理器
        config_manager = ConfigManager("./demo_configs")
        
        # 注册新的代理配置
        agent_config = AgentConfig(
            name="CustomAgent",
            class_path="mobius_multi_agent.example.math_agent.MathAgent",
            description="自定义数学计算代理",
            capabilities=["数学计算", "表达式求值"],
            input_schema={"expression": "str", "user_input": "optional[str]"}
        )
        
        success = await config_manager.register_agent(agent_config)
        logger.info(f"注册代理配置: {success}")
        
        # 获取配置摘要
        summary = config_manager.get_config_summary()
        logger.info(f"配置摘要: {summary}")
        
    except ImportError:
        logger.warning("配置管理器模块未找到，跳过演示")


async def main():
    """主演示函数"""
    
    logger.info("=== 增强版 Supervisor Agent 系统演示 ===")
    
    # 1. 基本功能演示
    logger.info("\n1. 基本功能演示")
    await demo_enhanced_supervisor()
    
    # 2. 依赖关系管理演示
    logger.info("\n2. 依赖关系管理演示")
    await demo_dependency_management()
    
    # 3. 检查点管理演示
    logger.info("\n3. 检查点管理演示")
    await demo_checkpoint_management()
    
    # 4. 配置管理演示
    logger.info("\n4. 配置管理演示")
    await demo_config_management()
    
    logger.info("\n=== 演示完成 ===")


if __name__ == "__main__":
    asyncio.run(main())
