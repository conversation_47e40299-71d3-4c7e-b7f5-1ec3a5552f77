[{"name": "simple_task", "description": "简单任务场景，适用于单步或少量步骤的任务", "template_mappings": {"plan_generation": "plan_generation_default", "result_evaluation": "result_evaluation_simple"}, "default_context": {"max_tasks": 3, "complexity_level": "low", "require_user_approval": false}, "conditions": {"task_complexity": "low", "estimated_steps": "<=3"}}, {"name": "complex_task", "description": "复杂任务场景，适用于多步骤、多代理协作的任务", "template_mappings": {"plan_generation": "plan_generation_advanced", "result_evaluation": "result_evaluation_detailed", "user_interaction": "user_interaction_detailed"}, "default_context": {"max_tasks": 10, "complexity_level": "high", "require_user_approval": true, "enable_parallel_execution": true}, "conditions": {"task_complexity": "high", "estimated_steps": ">5"}}, {"name": "data_analysis", "description": "数据分析场景，专门用于数据处理和分析任务", "template_mappings": {"plan_generation": "plan_generation_data_analysis", "result_evaluation": "result_evaluation_data_quality"}, "default_context": {"data_validation_required": true, "quality_threshold": 0.9, "include_visualization": true}, "conditions": {"task_type": "data_analysis", "involves_data": true}}, {"name": "error_recovery", "description": "错误恢复场景，用于处理任务执行失败的情况", "template_mappings": {"plan_generation": "plan_generation_recovery", "error_handling": "error_handling_detailed", "plan_revision": "plan_revision_recovery"}, "default_context": {"max_retries": 3, "fallback_strategy": "simplified_approach", "user_notification_required": true}, "conditions": {"has_failed_tasks": true, "retry_count": ">0"}}]