作为多代理系统监督器，请分析用户查询并生成详细的执行计划。

## 用户请求分析
用户查询: {{ user_query }}
{% if user_context %}
用户上下文: {{ user_context }}
{% endif %}

## 系统资源
可用代理: {{ available_agents | join(', ') }}
可用工具: {{ available_tools | join(', ') }}
{% if system_constraints %}
系统约束: {{ system_constraints }}
{% endif %}

## 计划生成要求

### 1. 任务分解
请将用户请求分解为具体的子任务，每个子任务应该：
- 有明确的输入和输出
- 可以由单个代理独立完成
- 具有可验证的完成标准

### 2. 依赖关系设计
请设计任务间的依赖关系，支持：
- 顺序依赖：任务B必须在任务A完成后执行
- 条件依赖：基于任务A的结果决定是否执行任务B
- 数据依赖：任务B需要任务A的输出数据
- 资源依赖：任务B需要任务A释放的资源

### 3. 代理分配
为每个任务分配最适合的代理：
{% for agent in available_agents %}
- {{ agent }}: 适用于 {{ agent_capabilities.get(agent, '通用任务') }}
{% endfor %}

### 4. 输入格式规范
{% if agent_input_schemas %}
各代理的输入格式要求：
{% for agent, schema in agent_input_schemas.items() %}
- {{ agent }}: {{ schema }}
{% endfor %}
{% endif %}

### 5. 质量控制
请确保：
- 任务描述清晰具体，避免歧义
- 依赖关系形成有效的DAG（无环图）
- 代理输入格式符合规范
- 包含适当的错误处理和重试策略

## 输出格式
请以JSON格式返回执行计划，包含以下字段：
- description: 计划总体描述
- subtasks: 子任务列表
- execution_order: 建议的执行顺序
- estimated_total_duration: 预估总时间（秒）

每个子任务包含：
- id: 任务唯一标识
- description: 任务描述
- agent_type: 执行代理类型
- agent_input: 代理输入参数
- dependencies: 依赖关系列表
- priority: 优先级（1-10）
- estimated_duration: 预估时间（秒）
