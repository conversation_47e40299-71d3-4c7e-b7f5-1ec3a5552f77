"""Hatch build hook to capture version information."""

import importlib
import subprocess
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional, Type


def _create_build_hook_class() -> Optional[Type[Any]]:
    """Create the build hook class dynamically to avoid import issues."""
    try:
        # Use importlib to avoid static import analysis
        hatchling_module = importlib.import_module(
            "hatchling.builders.hooks.plugin.interface"
        )
        build_hook_interface = hatchling_module.BuildHookInterface

        class CustomBuildHook(build_hook_interface):  # type: ignore[misc,valid-type]
            """Custom build hook to generate version information."""

            PLUGIN_NAME = "custom"

            def initialize(self, version: str, build_data: Dict[str, Any]) -> None:
                """Initialize the build hook and generate version information."""
                print("Running version capture build hook...")
                generate_version_file()

        return CustomBuildHook
    except ImportError:
        return None


# Create the build hook class if hatchling is available
CustomBuildHook = _create_build_hook_class()


def get_git_commit_id() -> str:
    """Get the current Git commit ID."""
    try:
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            capture_output=True,
            text=True,
            check=True,
        )
        return result.stdout.strip()
    except (subprocess.CalledProcessError, FileNotFoundError):
        # Fallback for environments without git
        return "unknown"


def get_git_branch() -> str:
    """Get the current Git branch name."""
    try:
        result = subprocess.run(
            ["git", "rev-parse", "--abbrev-ref", "HEAD"],
            capture_output=True,
            text=True,
            check=True,
        )
        return result.stdout.strip()
    except (subprocess.CalledProcessError, FileNotFoundError):
        # Fallback for environments without git
        return "unknown"


def get_git_commit_time() -> str:
    """Get the current Git commit time."""
    try:
        result = subprocess.run(
            ["git", "log", "-1", "--format=%cd", "--date=iso"],
            capture_output=True,
            text=True,
            check=True,
        )
        return result.stdout.strip()
    except (subprocess.CalledProcessError, FileNotFoundError):
        # Fallback for environments without git
        return "unknown"


def generate_version_file() -> None:
    """Generate the _version.py file with Git build-time information."""
    git_commit = get_git_commit_id()
    git_branch = get_git_branch()
    git_commit_time = get_git_commit_time()
    build_time = datetime.now(timezone.utc).isoformat()

    version_content = f'''"""Auto-generated Git information."""

__git_commit__ = "{git_commit}"
__git_branch__ = "{git_branch}"
__git_commit_time__ = "{git_commit_time}"
__build_time__ = "{build_time}"
'''

    version_file = (
        Path(__file__).parent.parent / "src" / "mobius_common" / "_version.py"
    )
    version_file.parent.mkdir(parents=True, exist_ok=True)

    with open(version_file, "w") as f:
        f.write(version_content)

    print(f"Generated version file: {version_file}")
    print(f"Git commit: {git_commit}")
    print(f"Git branch: {git_branch}")
    print(f"Git commit time: {git_commit_time}")
    print(f"Build time: {build_time}")


# For direct execution (development/testing)
if __name__ == "__main__":
    generate_version_file()
