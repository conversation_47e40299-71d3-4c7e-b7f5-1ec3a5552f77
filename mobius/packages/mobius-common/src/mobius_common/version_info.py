"""Runtime Git information utilities for Mobius packages."""

try:
    # tach-ignore
    from ._version import (  # type: ignore[import-not-found,unused-ignore]
        __git_commit__,
        __git_branch__,
        __git_commit_time__,
        __build_time__,
    )
except ImportError:
    # Fallback when _version.py is not generated
    __git_commit__ = "unknown"
    __git_branch__ = "unknown"
    __git_commit_time__ = "unknown"
    __build_time__ = "unknown"


def get_git_info() -> dict[str, str]:
    """Get Git build information."""
    return {
        "git_commit": __git_commit__,
        "git_branch": __git_branch__,
        "git_commit_time": __git_commit_time__,
        "build_time": __build_time__,
    }


def get_git_string() -> str:
    """Get a formatted Git string for logging."""
    short_commit = __git_commit__[:8] if __git_commit__ != "unknown" else __git_commit__
    return f"commit: {short_commit}, branch: {__git_branch__}, committed: {__git_commit_time__}, built: {__build_time__}"
