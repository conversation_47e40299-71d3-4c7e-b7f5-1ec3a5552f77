[project]
name = "mobius-multi-agent-server"
version = "0.1.0"
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "click>=8.1.0",
    "mobius-common",
    "mobius-multi-agent",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mobius_multi_agent_server"]

[tool.uv.sources]
mobius-common = { workspace = true }
mobius-multi-agent = { workspace = true }

[project.scripts]
mobius-multi-agent-server = "mobius_multi_agent_server.main:cli"
