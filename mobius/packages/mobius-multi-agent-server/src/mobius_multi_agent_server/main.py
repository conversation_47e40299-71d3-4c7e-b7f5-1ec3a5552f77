"""FastAPI main application for Mobius multi-agent server."""

import logging
import sys
from collections.abc import Async<PERSON>tera<PERSON>
from contextlib import asynccontextmanager

import click
import mobius_multi_agent
import uvicorn
from fastapi import FastAPI, HTTPException, Request
from mobius_common.version_info import get_git_string

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    """Manage application lifespan events."""
    # Startup

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    version_str = get_git_string()
    logger.info(f"Mobius multi-agent server started ({version_str})")

    yield

    # Shutdown
    logger.info("Mobius multi-agent server shutting down")


app = FastAPI(
    title="Mobius multi-agent server",
    version="0.1.0",
    lifespan=lifespan,
)


@app.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint."""
    return {"status": "healthy", "service": "mobius-multi-agent-server"}


# TODO: This is just an example. Needs to be modified.
def foobar() -> dict[str, str]:
    return {
        "hello": mobius_multi_agent.hello(),
    }


# TODO: This is just an example. Needs to be modified.
@app.post("/example")
async def handle_example(_request: Request) -> dict[str, str]:
    """Handle example request."""
    try:
        return foobar()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error handling request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error") from e


@click.group()
def cli() -> None:
    """Mobius multi-agent server CLI."""
    pass


@cli.command()
@click.option(
    "--host",
    default="::",
    help="Host to bind to (overrides config, default is IPv6-compatible '::')",
)
@click.option(
    "--port",
    default=9090,
    type=int,
    help="Port to bind to (overrides config)",
)
@click.option(
    "--reload",
    is_flag=True,
    help="Enable auto-reload for development",
)
def serve(host: str, port: int, reload: bool) -> None:
    """Start the Mobius multi-agent server."""
    # config = get_config()
    # setup_logging(config)

    # Use CLI options or fall back to config
    # server_host = host or config.host
    # server_port = port or config.port
    server_host = host
    server_port = port

    git_str = get_git_string()
    logger.info(f"Starting server on {server_host}:{server_port} ({git_str})")

    try:
        uvicorn.run(
            "mobius_multi_agent_server.main:app",
            host=server_host,
            port=server_port,
            reload=reload,
            log_config=None,  # Use our custom logging setup
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)
