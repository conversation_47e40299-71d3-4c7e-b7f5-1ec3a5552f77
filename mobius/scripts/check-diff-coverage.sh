#!/usr/bin/env bash

# Diff coverage validation script
# Enforces configurable coverage requirement on modified lines only

set -euo pipefail

# Set coverage threshold (default: 60%)
DIFF_COVERAGE_THRESHOLD=${DIFF_COVERAGE_THRESHOLD:-60}

echo "🎯 Diff Coverage Check"
echo "===================================="
echo "📊 Coverage threshold: ${DIFF_COVERAGE_THRESHOLD}% (set DIFF_COVERAGE_THRESHOLD to override)"
echo ""

cd "$(dirname "$0")/.."

# Check if coverage.xml exists
if [ ! -f "coverage.xml" ]; then
    echo "❌ coverage.xml not found"
    echo "📝 Run coverage combination first:"
    echo "   ./scripts/combine-coverage.sh"
    exit 1
fi

echo "📊 Found coverage.xml, checking diff coverage..."

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Not in a git repository"
    exit 1
fi

# Set comparison target (use precise merge base SHA when available)
if [ -n "${CI_MERGE_REQUEST_DIFF_BASE_SHA:-}" ]; then
    # In GitLab CI merge request pipeline - use the exact merge base SHA
    COMPARE_TARGET="${CI_MERGE_REQUEST_DIFF_BASE_SHA}"
    echo "🔍 Comparing against merge base SHA: ${COMPARE_TARGET}"

    # Ensure the base commit is available (GitLab may use shallow clone)
    if ! git cat-file -e "${COMPARE_TARGET}" 2>/dev/null; then
        echo "📥 Fetching base commit ${COMPARE_TARGET}..."
        git fetch origin "${COMPARE_TARGET}" 2>/dev/null || {
            echo "⚠️ Failed to fetch base commit directly, trying to deepen repository..."
            git fetch --unshallow 2>/dev/null || git fetch --depth=50 2>/dev/null || true

            if ! git cat-file -e "${COMPARE_TARGET}" 2>/dev/null; then
                echo "❌ Base commit ${COMPARE_TARGET} not available after fetch"
                echo "📝 Falling back to default branch comparison"
                COMPARE_TARGET="origin/${CI_DEFAULT_BRANCH:-main}"
            fi
        }
    fi
elif [ -n "${CI_DEFAULT_BRANCH:-}" ]; then
    # In GitLab CI but not a merge request - use default branch
    COMPARE_TARGET="origin/${CI_DEFAULT_BRANCH}"
    echo "🔍 Comparing against default branch: ${COMPARE_TARGET}"
else
    # Local development - find merge base with target branch
    TARGET_BRANCH=${DIFF_COVER_TARGET_BRANCH:-"origin/main"}
    if ! git show-ref --verify --quiet refs/remotes/${TARGET_BRANCH}; then
        TARGET_BRANCH="origin/master"
        if ! git show-ref --verify --quiet refs/remotes/${TARGET_BRANCH}; then
            echo "❌ Neither origin/main nor origin/master found"
            echo "📝 Set DIFF_COVER_TARGET_BRANCH environment variable if using a different default branch"
            exit 1
        fi
    fi

    # Get the merge base commit SHA for precise comparison
    MERGE_BASE=$(git merge-base HEAD ${TARGET_BRANCH} 2>/dev/null) || {
        echo "❌ Failed to find merge base with ${TARGET_BRANCH}"
        echo "📝 Make sure ${TARGET_BRANCH} is up to date: git fetch origin"
        exit 1
    }

    COMPARE_TARGET="${MERGE_BASE}"
    echo "🔍 Comparing against merge base with ${TARGET_BRANCH}: ${COMPARE_TARGET}"
fi

# Run diff-cover with configurable threshold
echo ""
echo "🚀 Running diff-cover with ${DIFF_COVERAGE_THRESHOLD}% threshold..."
echo "=========================================="

# Capture diff-cover output and exit code
EXIT_CODE=0
DIFF_COVER_OUTPUT=$(uv run diff-cover coverage.xml --compare-branch="${COMPARE_TARGET}" --fail-under=${DIFF_COVERAGE_THRESHOLD} 2>&1) || EXIT_CODE=$?
echo "${DIFF_COVER_OUTPUT}"

# Extract coverage percentage for GitLab widget
*********************=$(echo "${DIFF_COVER_OUTPUT}" | grep -o "Coverage: [0-9]\+%" | grep -o "[0-9]\+" | head -1) || true

if [ -n "${*********************}" ]; then
    echo ""
    echo "📈 Diff Coverage Result: ${*********************}%"
else
    echo ""
    echo "📈 Diff Coverage Result: No coverage data found"
fi

# Check if diff-cover failed
if [ "${EXIT_CODE}" -ne 0 ]; then
    echo ""
    echo "❌ Diff coverage check FAILED"
    echo "📝 Modified lines must have at least ${DIFF_COVERAGE_THRESHOLD}% test coverage"
    echo "💡 Write tests for the uncovered lines shown above"
    exit "${EXIT_CODE}"
fi

echo ""
echo "✅ Diff coverage check PASSED"
echo "🎉 All modified lines meet the ${DIFF_COVERAGE_THRESHOLD}% coverage requirement"
