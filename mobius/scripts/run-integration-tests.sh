#!/bin/bash

# Integration test runner script
# Shortcut for running integration tests

set -e

echo "🧪 Integration Tests"
echo "=================================="

cd "$(dirname "$0")/.."

# Start up external dependencies here
# TODO

# Install dependencies and run tests
echo "📦 Installing test dependencies..."
uv sync --all-packages --group dev --frozen

echo ""
echo "🧪 Running integration tests..."
export COVERAGE_FILE=.coverage.integration
uv run python -m pytest tests/integration/ -v -s --tb=short --junitxml=test-results-integration.xml

echo ""
echo "✅ Integration tests completed!"
