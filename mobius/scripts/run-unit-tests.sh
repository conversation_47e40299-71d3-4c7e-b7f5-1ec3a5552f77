#!/bin/bash

# Unit test runner script
# Shortcut for running unit tests

set -e

echo "🧪 Unit Tests"
echo "============================="

cd "$(dirname "$0")/.."

# Install dependencies
echo "📦 Installing test dependencies..."
uv sync --all-packages --group dev --frozen

echo ""
echo "🧪 Running unit tests..."
export COVERAGE_FILE=.coverage.unit
uv run python -m pytest tests/unit/ -v --tb=short --junitxml=test-results-unit.xml

echo ""
echo "✅ Unit tests completed!"
