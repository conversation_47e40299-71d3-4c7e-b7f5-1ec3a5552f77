# Vulture whitelist
# This file contains false positives that should be ignored by vulture

# Mock attributes used in tests - these are set but not directly accessed
# because they configure mock behavior
_.return_value  # Mock return values in tests
_.side_effect  # Mock side effects in tests

# FastAPI endpoints and CLI commands - used by framework/CLI but not directly called
startup_event  # FastAPI startup event handler
health_check  # FastAPI health check endpoint
handle_example  # FastAPI example endpoint
serve  # FastAPI CLI command to start the server

# Pydantic validators - called by framework
# Example: validate_log_level  # Pydantic field validator
cls  # Pydantic classmethod parameter

# Test fixtures - used by pytest framework
# Example: setup_test_logging
