---
description: 开发工作流程和工具使用指南
---

# 开发工作流程

基于项目 [scripts/](mdc:scripts/) 目录的开发工具链和工作流程指南。

## 环境设置

### 初始开发环境

```bash
# 运行开发环境设置脚本
./scripts/setup-dev.sh

# 手动设置步骤
uv sync --all-packages --group dev --frozen
uv run pre-commit install
```

### 依赖管理

```bash
# 安装所有依赖（包含开发依赖）
uv sync --all-packages --group dev

# 只安装生产依赖
uv sync --all-packages

# 添加新依赖到特定包
cd packages/mobius-common
uv add "new-dependency>=1.0.0"

# 添加开发依赖到根项目
uv add --group dev "new-dev-tool>=1.0.0"
```

## 代码质量检查

### lint.sh 脚本使用

```bash
# 运行所有检查并自动修复
./scripts/lint.sh

# 只运行检查，不自动修复
./scripts/lint.sh --check

# 各工具单独运行
uv run black .                    # 格式化代码
uv run black --check .            # 检查代码格式
uv run ruff check --fix           # Linting 并自动修复
uv run ruff check                 # 只检查 Linting
uv run mypy                       # 类型检查
uv run vulture                    # 死代码检测
uv run tach check-external        # 依赖验证
```

### Pre-commit 钩子

```yaml
# .pre-commit-config.yaml 示例
repos:
  - repo: local
    hooks:
      - id: black
        name: Black
        entry: uv run black
        language: system
        types: [python]
      
      - id: ruff
        name: Ruff
        entry: uv run ruff check --fix
        language: system
        types: [python]
      
      - id: mypy
        name: MyPy
        entry: uv run mypy
        language: system
        types: [python]
```

## 测试工作流程

### 测试脚本执行

```bash
# 运行单元测试
./scripts/run-unit-tests.sh

# 运行集成测试
./scripts/run-integration-tests.sh

# 手动测试命令
uv run pytest tests/unit/          # 单元测试
uv run pytest tests/integration/   # 集成测试
uv run pytest -m "not slow"        # 跳过慢速测试
uv run pytest --cov               # 生成覆盖率报告
```

### 覆盖率检查

```bash
# 检查差异覆盖率
./scripts/check-diff-coverage.sh

# 合并覆盖率报告
./scripts/combine-coverage.sh

# 生成覆盖率报告
uv run coverage report --show-missing
uv run coverage html  # 生成 HTML 报告
```

## 开发最佳实践

### 代码提交流程

1. **开发前检查**
   ```bash
   # 确保环境是最新的
   uv sync --all-packages --group dev --frozen
   
   # 运行所有测试确保基线正常
   ./scripts/run-unit-tests.sh
   ./scripts/lint.sh --check
   ```

2. **开发过程中**
   ```bash
   # 频繁运行相关测试
   uv run pytest tests/unit/relevant_module/
   
   # 保持代码质量
   ./scripts/lint.sh  # 自动修复格式问题
   ```

3. **提交前检查**
   ```bash
   # 完整的质量检查
   ./scripts/lint.sh --check
   ./scripts/run-unit-tests.sh
   ./scripts/run-integration-tests.sh
   
   # 检查依赖关系
   uv run tach check-external
   ```

### 分支开发策略

```bash
# 创建功能分支
git checkout -b feature/new-agent-capability

# 开发过程中定期同步主分支
git checkout main
git pull origin main
git checkout feature/new-agent-capability
git rebase main

# 提交前最终检查
./scripts/lint.sh --check
./scripts/run-unit-tests.sh
```

### 代码审查清单

- [ ] 代码格式符合 Black 标准
- [ ] 通过所有 Ruff linting 检查
- [ ] 没有 MyPy 类型错误
- [ ] 测试覆盖率达到要求
- [ ] 新功能有对应的测试
- [ ] 文档字符串完整且准确
- [ ] 没有死代码（Vulture 检查通过）
- [ ] 依赖关系合理（Tach 检查通过）

## 发布工作流程

### 版本管理

```bash
# 检查当前版本
uv run python -c "from mobius_common.version_info import get_git_string; print(get_git_string())"

# 更新版本号（在各个 pyproject.toml 中）
# 1. packages/mobius-common/pyproject.toml
# 2. packages/mobius-multi-agent/pyproject.toml  
# 3. packages/mobius-multi-agent-server/pyproject.toml
# 4. pyproject.toml (根项目)
```

### 构建和部署

```bash
# 构建所有包
uv build

# 构建特定包
cd packages/mobius-common
uv build

# 运行服务器
uv run mobius-multi-agent-server serve --host 0.0.0.0 --port 9090
```

## 故障排除

### 常见问题解决

1. **依赖冲突**
   ```bash
   # 清理并重新安装
   rm -rf .venv
   uv sync --all-packages --group dev
   ```

2. **测试失败**
   ```bash
   # 详细输出
   uv run pytest -v -s tests/failing_test.py
   
   # 调试模式
   uv run pytest --pdb tests/failing_test.py
   ```

3. **类型检查错误**
   ```bash
   # 显示详细类型信息
   uv run mypy --show-error-codes --show-error-context
   ```

4. **性能分析**
   ```bash
   # 慢速测试分析
   uv run pytest --durations=10
   
   # 覆盖率分析
   uv run coverage report --sort=miss
   ```

### 开发环境维护

```bash
# 定期更新依赖
uv lock --upgrade

# 清理缓存
uv cache clean

# 检查安全漏洞
uv run safety check

# 更新 pre-commit 钩子
uv run pre-commit autoupdate
```