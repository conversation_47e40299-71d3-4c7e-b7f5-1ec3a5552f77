---
description: pytest 测试最佳实践和模式
---

# 测试最佳实践

基于 [conftest.py](mdc:tests/conftest.py) 和 pytest 配置的测试开发指南。

## 测试结构

### 目录组织

```
tests/
├── conftest.py              # 全局测试配置和夹具
├── unit/                    # 单元测试
│   ├── __init__.py
│   ├── mobius/
│   │   └── test_mobius.py
│   └── mobius-multi-agent/
│       ├── supervisor/
│       └── test_mobius_multi_agent.py
└── integration/             # 集成测试
    ├── conftest.py         # 集成测试专用配置
    ├── __init__.py
    └── test_integration.py
```

### 测试分类标记

```python
import pytest

# 单元测试
@pytest.mark.unit
def test_user_validation():
    """测试用户验证逻辑。"""
    pass

# 集成测试
@pytest.mark.integration  
def test_api_endpoint():
    """测试 API 端点集成。"""
    pass

# 慢速测试
@pytest.mark.slow
def test_large_dataset_processing():
    """测试大数据集处理。"""
    pass
```

## pytest 配置模式

### conftest.py 基础设置

```python
import asyncio
import pytest
from collections.abc import AsyncGenerator, Generator
from fastapi.testclient import TestClient
from httpx import AsyncClient

from mobius_multi_agent_server.main import app

@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """为整个测试会话创建事件循环。"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        yield loop
    finally:
        loop.close()

@pytest.fixture
def test_client() -> TestClient:
    """创建 FastAPI 测试客户端。"""
    return TestClient(app)

@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """创建异步 HTTP 客户端。"""
    async with AsyncClient(app=app, base_url="http://testserver") as client:
        yield client
```

### 数据库测试夹具

```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from mobius.database import Base, get_db

# 测试数据库引擎
TEST_DATABASE_URL = "sqlite:///:memory:"

@pytest.fixture(scope="function")
def test_db():
    """为每个测试函数创建独立的数据库。"""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    # 创建会话
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    def override_get_db():
        try:
            db = TestingSessionLocal()
            yield db
        finally:
            db.close()
    
    # 覆盖依赖
    app.dependency_overrides[get_db] = override_get_db
    
    yield TestingSessionLocal()
    
    # 清理
    app.dependency_overrides.clear()
    Base.metadata.drop_all(bind=engine)
```

## 单元测试模式

### 函数测试

```python
import pytest
from unittest.mock import Mock, patch

from mobius_multi_agent.processor import DataProcessor
from mobius_multi_agent.exceptions import ProcessingError

class TestDataProcessor:
    """数据处理器测试类。"""
    
    def setup_method(self):
        """每个测试方法前的设置。"""
        self.processor = DataProcessor()
    
    def test_process_valid_data(self):
        """测试处理有效数据。"""
        # 准备
        input_data = {"key": "value", "number": 42}
        expected_result = {"processed": True, "count": 1}
        
        # 执行
        result = self.processor.process(input_data)
        
        # 验证
        assert result["processed"] is True
        assert result["count"] == 1
        assert "timestamp" in result
    
    def test_process_invalid_data_raises_error(self):
        """测试处理无效数据抛出异常。"""
        # 准备
        invalid_data = {"missing_required_field": True}
        
        # 执行和验证
        with pytest.raises(ProcessingError) as exc_info:
            self.processor.process(invalid_data)
        
        assert "required field missing" in str(exc_info.value)
    
    @pytest.mark.parametrize("input_value,expected", [
        (0, "zero"),
        (1, "one"),
        (2, "two"),
        (-1, "negative"),
    ])
    def test_number_to_text_conversion(self, input_value, expected):
        """参数化测试数字转文本转换。"""
        result = self.processor.number_to_text(input_value)
        assert result == expected
```

### 异步函数测试

```python
import pytest
from unittest.mock import AsyncMock

from mobius_multi_agent.async_processor import AsyncDataProcessor

class TestAsyncDataProcessor:
    """异步数据处理器测试类。"""
    
    @pytest.fixture
    def processor(self):
        """创建处理器实例。"""
        return AsyncDataProcessor()
    
    @pytest.mark.asyncio
    async def test_async_process_data(self, processor):
        """测试异步数据处理。"""
        # 准备
        input_data = {"async_task": True}
        
        # 执行
        result = await processor.process_async(input_data)
        
        # 验证
        assert result["status"] == "completed"
        assert "processing_time" in result
    
    @pytest.mark.asyncio
    async def test_async_process_with_timeout(self, processor):
        """测试异步处理超时。"""
        with patch.object(processor, 'external_api_call', new_callable=AsyncMock) as mock_api:
            # 模拟超时
            mock_api.side_effect = asyncio.TimeoutError()
            
            with pytest.raises(ProcessingError):
                await processor.process_with_external_api({"data": "test"})
```

## 集成测试模式

### API 端点测试

```python
import pytest
from httpx import AsyncClient
from fastapi import status

class TestAPIEndpoints:
    """API 端点集成测试。"""
    
    @pytest.mark.integration
    async def test_health_check(self, async_client: AsyncClient):
        """测试健康检查端点。"""
        response = await async_client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "mobius-multi-agent-server"
    
    @pytest.mark.integration
    async def test_process_request_success(self, async_client: AsyncClient):
        """测试处理请求成功场景。"""
        # 准备请求数据
        request_data = {
            "agent_id": "test_agent",
            "task_data": {"operation": "analyze", "input": "test data"}
        }
        
        # 发送请求
        response = await async_client.post("/api/v1/process", json=request_data)
        
        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert "result" in data
    
    @pytest.mark.integration
    async def test_process_request_validation_error(self, async_client: AsyncClient):
        """测试处理请求验证错误。"""
        # 准备无效请求数据
        invalid_data = {"missing_required_fields": True}
        
        # 发送请求
        response = await async_client.post("/api/v1/process", json=invalid_data)
        
        # 验证错误响应
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "detail" in data
```

### 多代理系统集成测试

```python
import pytest
from unittest.mock import AsyncMock, patch

from mobius_multi_agent.supervisor import TaskSupervisor
from mobius_multi_agent.registry import AgentRegistry

class TestMultiAgentIntegration:
    """多代理系统集成测试。"""
    
    @pytest.fixture
    async def agent_system(self):
        """创建完整的代理系统。"""
        registry = AgentRegistry()
        supervisor = TaskSupervisor(registry)
        
        # 注册测试代理
        mock_agent = AsyncMock()
        mock_agent.process.return_value = {"result": "test_output"}
        mock_agent.get_capabilities.return_value = ["analyze", "process"]
        
        registry.register_agent("test_agent", mock_agent)
        
        return supervisor, registry, mock_agent
    
    @pytest.mark.integration
    async def test_task_execution_flow(self, agent_system):
        """测试完整的任务执行流程。"""
        supervisor, registry, mock_agent = agent_system
        
        # 提交任务
        task_result = await supervisor.submit_task(
            task_id="test_task_001",
            agent_id="test_agent", 
            task_data={"operation": "test"}
        )
        
        # 验证任务执行
        assert task_result.status == TaskStatus.COMPLETED
        assert task_result.result == {"result": "test_output"}
        mock_agent.process.assert_called_once_with({"operation": "test"})
```

## Mock 和 Patch 模式

### 外部依赖模拟

```python
import pytest
from unittest.mock import Mock, patch, AsyncMock
import httpx

@pytest.fixture
def mock_external_api():
    """模拟外部 API 调用。"""
    with patch('httpx.AsyncClient') as mock_client:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        yield mock_client

@pytest.mark.asyncio
async def test_external_api_integration(mock_external_api):
    """测试外部 API 集成。"""
    from mobius_multi_agent.external import ExternalAPIClient
    
    client = ExternalAPIClient()
    result = await client.send_request({"data": "test"})
    
    assert result["status"] == "success"
    mock_external_api.return_value.__aenter__.return_value.post.assert_called_once()
```

## 覆盖率和质量

### pytest 配置

```toml
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --cov --cov-report=term-missing"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (integration tests)",
    "unit: marks tests as unit tests", 
    "integration: marks tests as integration tests",
]
asyncio_mode = "auto"
```

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 只运行单元测试
uv run pytest -m unit

# 只运行集成测试
uv run pytest -m integration

# 运行特定文件
uv run pytest tests/unit/test_specific.py

# 生成覆盖率报告
uv run pytest --cov --cov-report=html
```