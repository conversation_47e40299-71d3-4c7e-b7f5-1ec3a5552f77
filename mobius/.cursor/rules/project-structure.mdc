---
alwaysApply: true
---

# Mobius 项目结构和架构指南

Mobius 是一个基于 Python 的多代理 BI 系统，采用 monorepo 结构和工作区管理。

## 项目架构

- **根目录**: [pyproject.toml](mdc:pyproject.toml) - 主项目配置和工作区定义
- **工作区包**: `packages/` 目录下包含三个核心包
  - [mobius-common](mdc:packages/mobius-common/) - 共享组件和工具
  - [mobius-multi-agent](mdc:packages/mobius-multi-agent/) - 多代理核心逻辑
  - [mobius-multi-agent-server](mdc:packages/mobius-multi-agent-server/) - FastAPI 服务器
- **主入口**: [main.py](mdc:packages/mobius-multi-agent-server/src/mobius_multi_agent_server/main.py)

## 依赖管理

- 使用 `uv` 进行包管理和工作区依赖
- 工作区依赖在根 [pyproject.toml](mdc:pyproject.toml) 中定义
- 中国镜像源配置: `https://mirrors.ustc.edu.cn/pypi/simple`

## 目录结构约定

```
packages/
├── mobius-common/          # 共享组件
│   ├── src/mobius_common/  # 源代码
│   └── pyproject.toml      # 包配置
├── mobius-multi-agent/     # 多代理逻辑
│   ├── src/mobius_multi_agent/
│   │   ├── bi/             # BI 相关功能
│   │   ├── rag_doc/        # RAG 文档处理
│   │   └── supervisor/     # 监督器组件
│   └── pyproject.toml
└── mobius-multi-agent-server/  # Web 服务器
    ├── src/mobius_multi_agent_server/
    │   └── main.py         # FastAPI 应用
    └── pyproject.toml
```

## 开发工具链

- **代码质量**: 配置在根 [pyproject.toml](mdc:pyproject.toml) 中
  - Black (格式化): 88 字符行长度
  - Ruff (linting): Python 3.10+ 兼容
  - MyPy (类型检查): 严格模式
  - Vulture (死代码检测)
  - Tach (依赖验证)

- **测试**: [tests/](mdc:tests/) 目录结构
  - `unit/` - 单元测试
  - `integration/` - 集成测试
  - 使用 pytest + pytest-asyncio

- **脚本**: [scripts/](mdc:scripts/) 目录包含开发脚本
  - [setup-dev.sh](mdc:scripts/setup-dev.sh) - 开发环境设置
  - [lint.sh](mdc:scripts/lint.sh) - 代码质量检查

## 版本管理

- 所有包版本统一为 `0.1.0`
- 使用 [version_info.py](mdc:packages/mobius-common/src/mobius_common/version_info.py) 获取 Git 版本信息