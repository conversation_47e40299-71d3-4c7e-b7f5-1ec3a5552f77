---
description: FastAPI 应用和多代理服务器开发模式
---

# FastAPI 和多代理服务器开发模式

基于 [main.py](mdc:packages/mobius-multi-agent-server/src/mobius_multi_agent_server/main.py) 的 FastAPI 应用开发指南。

## 应用结构模式

### 生命周期管理

```python
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
import logging

@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    """管理应用生命周期事件。"""
    # 启动阶段
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger.info("Mobius 多代理服务器启动")
    
    yield
    
    # 关闭阶段
    logger.info("Mobius 多代理服务器关闭")

app = FastAPI(
    title="Mobius multi-agent server",
    version="0.1.0",
    lifespan=lifespan,
)
```

### 路由定义模式

```python
from fastapi import FastAPI, HTTPException, Request
import logging

logger = logging.getLogger(__name__)

@app.get("/health")
async def health_check() -> dict[str, str]:
    """健康检查端点。"""
    return {"status": "healthy", "service": "mobius-multi-agent-server"}

@app.post("/api/v1/process")
async def process_request(request: ProcessRequest) -> ProcessResponse:
    """处理多代理请求。
    
    Args:
        request: 处理请求对象
        
    Returns:
        处理结果响应
        
    Raises:
        HTTPException: 当请求处理失败时
    """
    try:
        result = await multi_agent_processor.process(request)
        return ProcessResponse(status="success", data=result)
    except ValidationError as e:
        logger.warning(f"请求验证失败: {e}")
        raise HTTPException(status_code=400, detail="请求格式无效") from e
    except Exception as e:
        logger.error(f"处理请求时发生错误: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误") from e
```

## 错误处理模式

### 分层错误处理

```python
from typing import Any
import logging

logger = logging.getLogger(__name__)

class MultiAgentError(Exception):
    """多代理系统基础异常类。"""
    pass

class AgentProcessingError(MultiAgentError):
    """代理处理错误。"""
    pass

class ConfigurationError(MultiAgentError):
    """配置错误。"""
    pass

@app.post("/api/v1/agent/execute")
async def execute_agent_task(
    task: AgentTask,
    agent_id: str,
) -> dict[str, Any]:
    """执行代理任务。"""
    try:
        # 验证代理配置
        agent_config = await get_agent_config(agent_id)
        if not agent_config:
            raise ConfigurationError(f"代理 {agent_id} 配置未找到")
        
        # 执行任务
        result = await agent_executor.execute(task, agent_config)
        return {"status": "completed", "result": result}
        
    except ConfigurationError as e:
        logger.error(f"代理配置错误: {e}")
        raise HTTPException(status_code=404, detail=str(e)) from e
    except AgentProcessingError as e:
        logger.error(f"代理处理错误: {e}")
        raise HTTPException(status_code=422, detail=str(e)) from e
    except HTTPException:
        raise  # 重新抛出 HTTP 异常
    except Exception as e:
        logger.error(f"执行代理任务时发生未预期错误: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误") from e
```

## CLI 集成模式

```python
import click
import uvicorn
import sys

@click.group()
def cli() -> None:
    """Mobius 多代理服务器 CLI。"""
    pass

@cli.command()
@click.option("--host", default="::", help="绑定主机地址")
@click.option("--port", default=9090, type=int, help="绑定端口")
@click.option("--reload", is_flag=True, help="启用开发模式自动重载")
def serve(host: str, port: int, reload: bool) -> None:
    """启动 Mobius 多代理服务器。"""
    logger.info(f"在 {host}:{port} 启动服务器")
    
    try:
        uvicorn.run(
            "mobius_multi_agent_server.main:app",
            host=host,
            port=port,
            reload=reload,
            log_config=None,  # 使用自定义日志配置
        )
    except KeyboardInterrupt:
        logger.info("服务器被用户停止")
    except Exception as e:
        logger.error(f"服务器错误: {e}")
        sys.exit(1)
```

## 多代理架构模式

### 代理管理器

```python
from typing import Protocol
from dataclasses import dataclass

class Agent(Protocol):
    """代理接口定义。"""
    
    async def process(self, input_data: dict[str, Any]) -> dict[str, Any]:
        """处理输入数据。"""
        ...
    
    def get_capabilities(self) -> list[str]:
        """获取代理能力列表。"""
        ...

@dataclass
class AgentRegistry:
    """代理注册表。"""
    agents: dict[str, Agent]
    
    def register_agent(self, agent_id: str, agent: Agent) -> None:
        """注册代理。"""
        self.agents[agent_id] = agent
    
    async def execute_task(
        self,
        agent_id: str,
        task_data: dict[str, Any],
    ) -> dict[str, Any]:
        """执行代理任务。"""
        if agent_id not in self.agents:
            raise AgentProcessingError(f"代理 {agent_id} 未注册")
        
        agent = self.agents[agent_id]
        return await agent.process(task_data)
```

### 监督器模式

```python
from enum import Enum

class TaskStatus(Enum):
    """任务状态枚举。"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class TaskResult:
    """任务结果。"""
    task_id: str
    status: TaskStatus
    result: dict[str, Any] | None = None
    error: str | None = None

class TaskSupervisor:
    """任务监督器。"""
    
    def __init__(self, agent_registry: AgentRegistry) -> None:
        self.agent_registry = agent_registry
        self.active_tasks: dict[str, TaskResult] = {}
    
    async def submit_task(
        self,
        task_id: str,
        agent_id: str,
        task_data: dict[str, Any],
    ) -> TaskResult:
        """提交任务给代理执行。"""
        task_result = TaskResult(task_id=task_id, status=TaskStatus.PENDING)
        self.active_tasks[task_id] = task_result
        
        try:
            task_result.status = TaskStatus.RUNNING
            result = await self.agent_registry.execute_task(agent_id, task_data)
            task_result.status = TaskStatus.COMPLETED
            task_result.result = result
        except Exception as e:
            task_result.status = TaskStatus.FAILED
            task_result.error = str(e)
            logger.error(f"任务 {task_id} 执行失败: {e}")
        
        return task_result
```

## 配置管理模式

```python
from pydantic import BaseSettings, Field

class ServerSettings(BaseSettings):
    """服务器配置。"""
    host: str = Field(default="::", description="服务器主机地址")
    port: int = Field(default=9090, description="服务器端口")
    reload: bool = Field(default=False, description="开发模式重载")
    log_level: str = Field(default="INFO", description="日志级别")
    
    class Config:
        env_prefix = "MOBIUS_"
        env_file = ".env"

class AgentSettings(BaseSettings):
    """代理配置。"""
    max_concurrent_tasks: int = Field(default=10, description="最大并发任务数")
    task_timeout: int = Field(default=300, description="任务超时时间(秒)")
    
    class Config:
        env_prefix = "MOBIUS_AGENT_"

# 全局配置实例
server_settings = ServerSettings()
agent_settings = AgentSettings()
```