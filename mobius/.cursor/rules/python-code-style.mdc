---
globs: *.py,*.pyi
---

# Python 代码风格和质量规范

## 代码格式化 (Black)

- **行长度**: 88 字符
- **目标版本**: Python 3.9+
- **格式化命令**: `uv run black .`
- **检查命令**: `uv run black --check .`

```python
# 好的例子 - 符合 Black 格式
def process_data(
    input_data: dict[str, Any],
    config: Config,
    *,
    validate: bool = True,
) -> ProcessResult:
    """处理输入数据并返回结果。"""
    pass
```

## Linting 规范 (Ruff)

- **目标版本**: Python 3.10+
- **选择的规则**:
  - `E`, `W`: pycodestyle 错误和警告
  - `F`: pyflakes
  - `I`: isort (导入排序)
  - `B`: flake8-bugbear
  - `C4`: flake8-comprehensions
  - `UP`: pyupgrade

```python
# 导入排序 (isort)
import logging
import sys
from collections.abc import AsyncIterator
from contextlib import asynccontextmanager

import click
import uvicorn
from fastapi import FastAPI, HTTPException
```

## 类型注解 (MyPy)

- **严格模式**: 启用所有严格类型检查
- **必须的类型注解**: 函数参数、返回值、类属性
- **不允许**: `Any` 类型（除非必要）

```python
# 好的类型注解例子
from collections.abc import AsyncIterator
from typing import Protocol

class DataProcessor(Protocol):
    async def process(self, data: dict[str, str]) -> dict[str, Any]:
        """处理数据的协议定义。"""
        ...

async def handle_request(
    request: Request,
    processor: DataProcessor,
) -> dict[str, str]:
    """处理请求并返回响应。"""
    result = await processor.process(request.data)
    return {"status": "success", "data": result}
```

## 文档字符串

- **格式**: Google 风格的文档字符串
- **语言**: 中文（根据项目需求）
- **必须包含**: 函数用途、参数说明、返回值说明

```python
def calculate_metrics(
    data: list[dict[str, float]],
    weights: dict[str, float] | None = None,
) -> dict[str, float]:
    """计算数据指标。
    
    Args:
        data: 包含数值数据的字典列表
        weights: 可选的权重字典，用于加权计算
        
    Returns:
        包含计算得出的各项指标的字典
        
    Raises:
        ValueError: 当数据格式不正确时
    """
    pass
```

## 异常处理

- **具体异常**: 优先使用具体的异常类型
- **异常链**: 使用 `raise ... from ...` 保持异常链
- **日志记录**: 在捕获异常时记录适当的日志

```python
import logging

logger = logging.getLogger(__name__)

async def process_request(request: Request) -> dict[str, str]:
    """处理请求的示例函数。"""
    try:
        result = await some_operation(request)
        return {"status": "success", "data": result}
    except ValidationError as e:
        logger.warning(f"请求验证失败: {e}")
        raise HTTPException(status_code=400, detail="请求格式无效") from e
    except DatabaseError as e:
        logger.error(f"数据库操作失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误") from e
    except Exception as e:
        logger.error(f"未预期的错误: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误") from e
```

## 异步编程

- **一致性**: 在异步函数中使用 `async`/`await`
- **上下文管理器**: 优先使用异步上下文管理器
- **生命周期管理**: 正确管理异步资源

```python
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    """管理应用生命周期事件。"""
    # 启动
    logger.info("应用启动")
    yield
    # 关闭
    logger.info("应用关闭")
```

## 代码组织

- **模块级别**: 每个模块应该有清晰的职责
- **函数长度**: 保持函数简洁，单一职责
- **常量**: 使用大写字母命名常量

```python
# 常量定义
DEFAULT_HOST = "::"
DEFAULT_PORT = 9090
MAX_RETRY_ATTEMPTS = 3

# 配置类
@dataclass(frozen=True)
class ServerConfig:
    """服务器配置类。"""
    host: str = DEFAULT_HOST
    port: int = DEFAULT_PORT
    reload: bool = False
```