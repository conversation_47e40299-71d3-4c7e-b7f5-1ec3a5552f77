"""Integration test configuration and fixtures."""

import subprocess
import time
from collections.abc import Generator

import pytest
import requests


def wait_for_server_ready(host: str, port: int, timeout: int = 30) -> None:
    """Wait for the server to be ready by checking the health endpoint."""
    url = f"http://{host}:{port}/health"
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=1)
            if response.status_code == 200:
                return
        except (requests.ConnectionError, requests.Timeout):
            pass
        time.sleep(0.1)

    raise TimeoutError(f"Server did not become ready within {timeout} seconds")


@pytest.fixture(scope="session", autouse=True)
def start_mobius_multi_agent_server() -> Generator[str, None, None]:
    """Fixture to start and stop the Mobius Multi-Agent server."""
    host = "127.0.0.1"
    port = 8000
    base_url = f"http://{host}:{port}"

    print("Starting Mobius Multi-Agent server...")

    # Start server as a subprocess
    server_process = subprocess.Popen(
        ["mobius-multi-agent-server", "serve", "--host", host, "--port", str(port)]
    )

    # Wait for server to be ready using health check
    wait_for_server_ready(host, port)

    print("Mobius Multi-Agent server started.")

    yield base_url

    # Cleanup: terminate the server process
    print("Stopping Mobius Multi-Agent server...")
    server_process.terminate()

    try:
        server_process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        server_process.kill()
        server_process.wait()

    print("Mobius Multi-Agent server stopped.")
